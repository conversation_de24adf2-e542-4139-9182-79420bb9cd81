-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:2:1-72:12
INJECTED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:2:1-72:12
INJECTED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:2:1-72:12
INJECTED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:2:1-72:12
MERGED from [com.google.android.material:material:1.9.0] E:\android stuin\GradleRepository\caches\transforms-3\2599056ea79509e6de5af09916640d19\transformed\material-1.9.0\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] E:\android stuin\GradleRepository\caches\transforms-3\3d61983c6ed78cd9cba6704a170797ab\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] E:\android stuin\GradleRepository\caches\transforms-3\181d1cc96c37340295cde1cf06f38d13\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat:1.6.1] E:\android stuin\GradleRepository\caches\transforms-3\27f944d5dd141e8c451c2518b7151c40\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] E:\android stuin\GradleRepository\caches\transforms-3\b5260fa74047262f3dbb1d8acbb8db43\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.3.6] E:\android stuin\GradleRepository\caches\transforms-3\b4d6351b0ffb103a9b1cd1c2bcdce657\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.6.0] E:\android stuin\GradleRepository\caches\transforms-3\98611db8f32b03bd9239b2a9833bb825\transformed\activity-1.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] E:\android stuin\GradleRepository\caches\transforms-3\5793b0da2d2b55d953187b3ec1fcbdae\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] E:\android stuin\GradleRepository\caches\transforms-3\9e497d5e9f54b6e90ad951dbab4c9ece\transformed\emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] E:\android stuin\GradleRepository\caches\transforms-3\acb93310fddccd383c579526aea6dc7a\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] E:\android stuin\GradleRepository\caches\transforms-3\c644e30b2e2c41e24dcfd3d40d996371\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] E:\android stuin\GradleRepository\caches\transforms-3\c1ec98f9cd862e71e056a28ef1f64d81\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.2.0] E:\android stuin\GradleRepository\caches\transforms-3\5862a699a5a5e69d26ba1ea3c76fc3c9\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] E:\android stuin\GradleRepository\caches\transforms-3\6f4d778fed03992390cb746343713da2\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] E:\android stuin\GradleRepository\caches\transforms-3\565351ad2c306bece7158d81ebfc0f75\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] E:\android stuin\GradleRepository\caches\transforms-3\cc6062d93917b2769f16c12e5fe73b62\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] E:\android stuin\GradleRepository\caches\transforms-3\a3ea855d6e0b829df442ae83b9b8108c\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] E:\android stuin\GradleRepository\caches\transforms-3\b03939f4246e2bcd725426a7c880dcbf\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] E:\android stuin\GradleRepository\caches\transforms-3\435385c488cb63738fe8aba042f89433\transformed\lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core-ktx:1.9.0] E:\android stuin\GradleRepository\caches\transforms-3\475449d9719f6cca2c2ce28387246fd4\transformed\core-ktx-1.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] E:\android stuin\GradleRepository\caches\transforms-3\c83f95e6a7172ab20792227ee195e8bf\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] E:\android stuin\GradleRepository\caches\transforms-3\d9880179adcb0a83e7e39cad97610f1e\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] E:\android stuin\GradleRepository\caches\transforms-3\42369eaf4818b85dbf2c9063b433610a\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.9.0] E:\android stuin\GradleRepository\caches\transforms-3\0b3d22ab099177b3472464321ad2c41c\transformed\core-1.9.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] E:\android stuin\GradleRepository\caches\transforms-3\dc0516c85ef70f445850a1dacd19d06a\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] E:\android stuin\GradleRepository\caches\transforms-3\e06aa37db755f95a071deab60dc4e10c\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:17:1-37:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] E:\android stuin\GradleRepository\caches\transforms-3\fb6ad7aca95c576931c285d006f3af1a\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.savedstate:savedstate:1.2.0] E:\android stuin\GradleRepository\caches\transforms-3\55c4936bfab28b66d8d3d5e855912f2b\transformed\savedstate-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.cardview:cardview:1.0.0] E:\android stuin\GradleRepository\caches\transforms-3\04982dfa78b345590af07a01319f4e04\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.startup:startup-runtime:1.1.1] E:\android stuin\GradleRepository\caches\transforms-3\36bbd0e1a22643c62452142bd1e3da0f\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] E:\android stuin\GradleRepository\caches\transforms-3\20321e8dcca55ada35e3ee2d579c82b3\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] E:\android stuin\GradleRepository\caches\transforms-3\78c62fc5998d1bed28640af0bce4b2e9\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] E:\android stuin\GradleRepository\caches\transforms-3\16e3506697fd534614cbbd4c04c60187\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] E:\android stuin\GradleRepository\caches\transforms-3\75b408fea13d0dcc08e4f7a1f9ac7e0a\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.1.0] E:\android stuin\GradleRepository\caches\transforms-3\253e507fc446b9696276bad0f8aac05d\transformed\core-runtime-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] E:\android stuin\GradleRepository\caches\transforms-3\dcb99d9e0ed4bd13553d2eef8b044143\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] E:\android stuin\GradleRepository\caches\transforms-3\d186d5bc5a2c011e298147b808ce8a5a\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] E:\android stuin\GradleRepository\caches\transforms-3\adeee3dc52fe82cc41bfb6cd4f5789e2\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] E:\android stuin\GradleRepository\caches\transforms-3\9f4d5964dee8aeae0c6fdd9d7ece3e51\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] E:\android stuin\GradleRepository\caches\transforms-3\764bd21af56b174408c98a6d2ff6f8aa\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.BLUETOOTH
ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:6:5-68
	android:name
		ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:6:22-65
uses-permission#android.permission.BLUETOOTH_ADMIN
ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:7:5-74
	android:name
		ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:7:22-71
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:8:5-81
	android:name
		ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:8:22-78
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:9:5-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:9:22-76
uses-permission#android.permission.ACCESS_BACKGROUND_LOCATION
ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:10:5-85
	android:name
		ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:10:22-82
uses-permission#android.permission.BLUETOOTH_SCAN
ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:13:5-14:58
	android:usesPermissionFlags
		ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:14:9-55
	android:name
		ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:13:22-70
uses-permission#android.permission.BLUETOOTH_ADVERTISE
ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:15:5-78
	android:name
		ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:15:22-75
uses-permission#android.permission.BLUETOOTH_CONNECT
ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:16:5-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:16:22-73
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:19:5-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:19:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:20:5-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:20:22-76
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:23:5-68
	android:name
		ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:23:22-65
uses-permission#android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS
ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:24:5-95
	android:name
		ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:24:22-92
uses-permission#android.permission.DISABLE_KEYGUARD
ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:25:5-75
	android:name
		ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:25:22-72
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:28:5-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:28:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_CONNECTED_DEVICE
ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:29:5-94
	android:name
		ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:29:22-91
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:30:5-78
	android:name
		ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:30:22-75
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:33:5-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:33:22-74
uses-permission#android.permission.USE_FULL_SCREEN_INTENT
ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:34:5-81
	android:name
		ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:34:22-78
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:37:5-81
	android:name
		ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:37:22-78
uses-permission#android.permission.QUERY_ALL_PACKAGES
ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:38:5-39:53
	tools:ignore
		ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:39:9-50
	android:name
		ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:38:22-74
application
ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:41:5-70:19
INJECTED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:41:5-70:19
MERGED from [com.google.android.material:material:1.9.0] E:\android stuin\GradleRepository\caches\transforms-3\2599056ea79509e6de5af09916640d19\transformed\material-1.9.0\AndroidManifest.xml:24:5-20
MERGED from [com.google.android.material:material:1.9.0] E:\android stuin\GradleRepository\caches\transforms-3\2599056ea79509e6de5af09916640d19\transformed\material-1.9.0\AndroidManifest.xml:24:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] E:\android stuin\GradleRepository\caches\transforms-3\181d1cc96c37340295cde1cf06f38d13\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] E:\android stuin\GradleRepository\caches\transforms-3\181d1cc96c37340295cde1cf06f38d13\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.2.0] E:\android stuin\GradleRepository\caches\transforms-3\9e497d5e9f54b6e90ad951dbab4c9ece\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] E:\android stuin\GradleRepository\caches\transforms-3\9e497d5e9f54b6e90ad951dbab4c9ece\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.9.0] E:\android stuin\GradleRepository\caches\transforms-3\0b3d22ab099177b3472464321ad2c41c\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.9.0] E:\android stuin\GradleRepository\caches\transforms-3\0b3d22ab099177b3472464321ad2c41c\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] E:\android stuin\GradleRepository\caches\transforms-3\e06aa37db755f95a071deab60dc4e10c\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] E:\android stuin\GradleRepository\caches\transforms-3\e06aa37db755f95a071deab60dc4e10c\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.startup:startup-runtime:1.1.1] E:\android stuin\GradleRepository\caches\transforms-3\36bbd0e1a22643c62452142bd1e3da0f\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] E:\android stuin\GradleRepository\caches\transforms-3\36bbd0e1a22643c62452142bd1e3da0f\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] E:\android stuin\GradleRepository\caches\transforms-3\78c62fc5998d1bed28640af0bce4b2e9\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] E:\android stuin\GradleRepository\caches\transforms-3\78c62fc5998d1bed28640af0bce4b2e9\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.9.0] E:\android stuin\GradleRepository\caches\transforms-3\0b3d22ab099177b3472464321ad2c41c\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:48:9-35
	android:label
		ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:46:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:44:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:47:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:50:9-29
	android:icon
		ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:45:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:42:9-35
	android:theme
		ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:49:9-54
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:43:9-65
meta-data#xposedmodule
ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:53:9-55:36
	android:value
		ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:55:13-33
	android:name
		ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:54:13-40
meta-data#xposeddescription
ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:56:9-58:50
	android:value
		ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:58:13-47
	android:name
		ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:57:13-45
meta-data#xposedminversion
ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:59:9-61:35
	android:value
		ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:61:13-32
	android:name
		ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:60:13-44
service#com.lanya.btmonitor.BluetoothForegroundService
ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:64:9-68:63
	android:enabled
		ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:66:13-35
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:67:13-37
	android:foregroundServiceType
		ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:68:13-60
	android:name
		ADDED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:65:13-55
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml
MERGED from [com.google.android.material:material:1.9.0] E:\android stuin\GradleRepository\caches\transforms-3\2599056ea79509e6de5af09916640d19\transformed\material-1.9.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.9.0] E:\android stuin\GradleRepository\caches\transforms-3\2599056ea79509e6de5af09916640d19\transformed\material-1.9.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] E:\android stuin\GradleRepository\caches\transforms-3\3d61983c6ed78cd9cba6704a170797ab\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] E:\android stuin\GradleRepository\caches\transforms-3\3d61983c6ed78cd9cba6704a170797ab\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] E:\android stuin\GradleRepository\caches\transforms-3\181d1cc96c37340295cde1cf06f38d13\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] E:\android stuin\GradleRepository\caches\transforms-3\181d1cc96c37340295cde1cf06f38d13\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.6.1] E:\android stuin\GradleRepository\caches\transforms-3\27f944d5dd141e8c451c2518b7151c40\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] E:\android stuin\GradleRepository\caches\transforms-3\27f944d5dd141e8c451c2518b7151c40\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] E:\android stuin\GradleRepository\caches\transforms-3\b5260fa74047262f3dbb1d8acbb8db43\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] E:\android stuin\GradleRepository\caches\transforms-3\b5260fa74047262f3dbb1d8acbb8db43\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] E:\android stuin\GradleRepository\caches\transforms-3\b4d6351b0ffb103a9b1cd1c2bcdce657\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] E:\android stuin\GradleRepository\caches\transforms-3\b4d6351b0ffb103a9b1cd1c2bcdce657\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.6.0] E:\android stuin\GradleRepository\caches\transforms-3\98611db8f32b03bd9239b2a9833bb825\transformed\activity-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.6.0] E:\android stuin\GradleRepository\caches\transforms-3\98611db8f32b03bd9239b2a9833bb825\transformed\activity-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] E:\android stuin\GradleRepository\caches\transforms-3\5793b0da2d2b55d953187b3ec1fcbdae\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] E:\android stuin\GradleRepository\caches\transforms-3\5793b0da2d2b55d953187b3ec1fcbdae\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] E:\android stuin\GradleRepository\caches\transforms-3\9e497d5e9f54b6e90ad951dbab4c9ece\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] E:\android stuin\GradleRepository\caches\transforms-3\9e497d5e9f54b6e90ad951dbab4c9ece\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] E:\android stuin\GradleRepository\caches\transforms-3\acb93310fddccd383c579526aea6dc7a\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] E:\android stuin\GradleRepository\caches\transforms-3\acb93310fddccd383c579526aea6dc7a\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] E:\android stuin\GradleRepository\caches\transforms-3\c644e30b2e2c41e24dcfd3d40d996371\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] E:\android stuin\GradleRepository\caches\transforms-3\c644e30b2e2c41e24dcfd3d40d996371\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] E:\android stuin\GradleRepository\caches\transforms-3\c1ec98f9cd862e71e056a28ef1f64d81\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] E:\android stuin\GradleRepository\caches\transforms-3\c1ec98f9cd862e71e056a28ef1f64d81\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.2.0] E:\android stuin\GradleRepository\caches\transforms-3\5862a699a5a5e69d26ba1ea3c76fc3c9\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] E:\android stuin\GradleRepository\caches\transforms-3\5862a699a5a5e69d26ba1ea3c76fc3c9\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] E:\android stuin\GradleRepository\caches\transforms-3\6f4d778fed03992390cb746343713da2\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] E:\android stuin\GradleRepository\caches\transforms-3\6f4d778fed03992390cb746343713da2\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] E:\android stuin\GradleRepository\caches\transforms-3\565351ad2c306bece7158d81ebfc0f75\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] E:\android stuin\GradleRepository\caches\transforms-3\565351ad2c306bece7158d81ebfc0f75\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] E:\android stuin\GradleRepository\caches\transforms-3\cc6062d93917b2769f16c12e5fe73b62\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] E:\android stuin\GradleRepository\caches\transforms-3\cc6062d93917b2769f16c12e5fe73b62\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] E:\android stuin\GradleRepository\caches\transforms-3\a3ea855d6e0b829df442ae83b9b8108c\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] E:\android stuin\GradleRepository\caches\transforms-3\a3ea855d6e0b829df442ae83b9b8108c\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] E:\android stuin\GradleRepository\caches\transforms-3\b03939f4246e2bcd725426a7c880dcbf\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] E:\android stuin\GradleRepository\caches\transforms-3\b03939f4246e2bcd725426a7c880dcbf\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] E:\android stuin\GradleRepository\caches\transforms-3\435385c488cb63738fe8aba042f89433\transformed\lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] E:\android stuin\GradleRepository\caches\transforms-3\435385c488cb63738fe8aba042f89433\transformed\lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-ktx:1.9.0] E:\android stuin\GradleRepository\caches\transforms-3\475449d9719f6cca2c2ce28387246fd4\transformed\core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.9.0] E:\android stuin\GradleRepository\caches\transforms-3\475449d9719f6cca2c2ce28387246fd4\transformed\core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] E:\android stuin\GradleRepository\caches\transforms-3\c83f95e6a7172ab20792227ee195e8bf\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] E:\android stuin\GradleRepository\caches\transforms-3\c83f95e6a7172ab20792227ee195e8bf\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] E:\android stuin\GradleRepository\caches\transforms-3\d9880179adcb0a83e7e39cad97610f1e\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] E:\android stuin\GradleRepository\caches\transforms-3\d9880179adcb0a83e7e39cad97610f1e\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] E:\android stuin\GradleRepository\caches\transforms-3\42369eaf4818b85dbf2c9063b433610a\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] E:\android stuin\GradleRepository\caches\transforms-3\42369eaf4818b85dbf2c9063b433610a\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.9.0] E:\android stuin\GradleRepository\caches\transforms-3\0b3d22ab099177b3472464321ad2c41c\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] E:\android stuin\GradleRepository\caches\transforms-3\0b3d22ab099177b3472464321ad2c41c\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] E:\android stuin\GradleRepository\caches\transforms-3\dc0516c85ef70f445850a1dacd19d06a\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] E:\android stuin\GradleRepository\caches\transforms-3\dc0516c85ef70f445850a1dacd19d06a\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] E:\android stuin\GradleRepository\caches\transforms-3\e06aa37db755f95a071deab60dc4e10c\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] E:\android stuin\GradleRepository\caches\transforms-3\e06aa37db755f95a071deab60dc4e10c\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] E:\android stuin\GradleRepository\caches\transforms-3\fb6ad7aca95c576931c285d006f3af1a\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] E:\android stuin\GradleRepository\caches\transforms-3\fb6ad7aca95c576931c285d006f3af1a\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.2.0] E:\android stuin\GradleRepository\caches\transforms-3\55c4936bfab28b66d8d3d5e855912f2b\transformed\savedstate-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.2.0] E:\android stuin\GradleRepository\caches\transforms-3\55c4936bfab28b66d8d3d5e855912f2b\transformed\savedstate-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cardview:cardview:1.0.0] E:\android stuin\GradleRepository\caches\transforms-3\04982dfa78b345590af07a01319f4e04\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] E:\android stuin\GradleRepository\caches\transforms-3\04982dfa78b345590af07a01319f4e04\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] E:\android stuin\GradleRepository\caches\transforms-3\36bbd0e1a22643c62452142bd1e3da0f\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] E:\android stuin\GradleRepository\caches\transforms-3\36bbd0e1a22643c62452142bd1e3da0f\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] E:\android stuin\GradleRepository\caches\transforms-3\20321e8dcca55ada35e3ee2d579c82b3\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] E:\android stuin\GradleRepository\caches\transforms-3\20321e8dcca55ada35e3ee2d579c82b3\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] E:\android stuin\GradleRepository\caches\transforms-3\78c62fc5998d1bed28640af0bce4b2e9\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] E:\android stuin\GradleRepository\caches\transforms-3\78c62fc5998d1bed28640af0bce4b2e9\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] E:\android stuin\GradleRepository\caches\transforms-3\16e3506697fd534614cbbd4c04c60187\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] E:\android stuin\GradleRepository\caches\transforms-3\16e3506697fd534614cbbd4c04c60187\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] E:\android stuin\GradleRepository\caches\transforms-3\75b408fea13d0dcc08e4f7a1f9ac7e0a\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] E:\android stuin\GradleRepository\caches\transforms-3\75b408fea13d0dcc08e4f7a1f9ac7e0a\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] E:\android stuin\GradleRepository\caches\transforms-3\253e507fc446b9696276bad0f8aac05d\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] E:\android stuin\GradleRepository\caches\transforms-3\253e507fc446b9696276bad0f8aac05d\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] E:\android stuin\GradleRepository\caches\transforms-3\dcb99d9e0ed4bd13553d2eef8b044143\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] E:\android stuin\GradleRepository\caches\transforms-3\dcb99d9e0ed4bd13553d2eef8b044143\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] E:\android stuin\GradleRepository\caches\transforms-3\d186d5bc5a2c011e298147b808ce8a5a\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] E:\android stuin\GradleRepository\caches\transforms-3\d186d5bc5a2c011e298147b808ce8a5a\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] E:\android stuin\GradleRepository\caches\transforms-3\adeee3dc52fe82cc41bfb6cd4f5789e2\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] E:\android stuin\GradleRepository\caches\transforms-3\adeee3dc52fe82cc41bfb6cd4f5789e2\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] E:\android stuin\GradleRepository\caches\transforms-3\9f4d5964dee8aeae0c6fdd9d7ece3e51\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] E:\android stuin\GradleRepository\caches\transforms-3\9f4d5964dee8aeae0c6fdd9d7ece3e51\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] E:\android stuin\GradleRepository\caches\transforms-3\764bd21af56b174408c98a6d2ff6f8aa\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] E:\android stuin\GradleRepository\caches\transforms-3\764bd21af56b174408c98a6d2ff6f8aa\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] E:\android stuin\GradleRepository\caches\transforms-3\9e497d5e9f54b6e90ad951dbab4c9ece\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] E:\android stuin\GradleRepository\caches\transforms-3\e06aa37db755f95a071deab60dc4e10c\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] E:\android stuin\GradleRepository\caches\transforms-3\e06aa37db755f95a071deab60dc4e10c\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.startup:startup-runtime:1.1.1] E:\android stuin\GradleRepository\caches\transforms-3\36bbd0e1a22643c62452142bd1e3da0f\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] E:\android stuin\GradleRepository\caches\transforms-3\36bbd0e1a22643c62452142bd1e3da0f\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] E:\android stuin\GradleRepository\caches\transforms-3\9e497d5e9f54b6e90ad951dbab4c9ece\transformed\emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] E:\android stuin\GradleRepository\caches\transforms-3\9e497d5e9f54b6e90ad951dbab4c9ece\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] E:\android stuin\GradleRepository\caches\transforms-3\9e497d5e9f54b6e90ad951dbab4c9ece\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] E:\android stuin\GradleRepository\caches\transforms-3\9e497d5e9f54b6e90ad951dbab4c9ece\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] E:\android stuin\GradleRepository\caches\transforms-3\9e497d5e9f54b6e90ad951dbab4c9ece\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] E:\android stuin\GradleRepository\caches\transforms-3\9e497d5e9f54b6e90ad951dbab4c9ece\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] E:\android stuin\GradleRepository\caches\transforms-3\9e497d5e9f54b6e90ad951dbab4c9ece\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] E:\android stuin\GradleRepository\caches\transforms-3\0b3d22ab099177b3472464321ad2c41c\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] E:\android stuin\GradleRepository\caches\transforms-3\0b3d22ab099177b3472464321ad2c41c\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] E:\android stuin\GradleRepository\caches\transforms-3\0b3d22ab099177b3472464321ad2c41c\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
permission#com.lanya.btmonitor.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] E:\android stuin\GradleRepository\caches\transforms-3\0b3d22ab099177b3472464321ad2c41c\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] E:\android stuin\GradleRepository\caches\transforms-3\0b3d22ab099177b3472464321ad2c41c\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] E:\android stuin\GradleRepository\caches\transforms-3\0b3d22ab099177b3472464321ad2c41c\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] E:\android stuin\GradleRepository\caches\transforms-3\0b3d22ab099177b3472464321ad2c41c\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] E:\android stuin\GradleRepository\caches\transforms-3\0b3d22ab099177b3472464321ad2c41c\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
uses-permission#com.lanya.btmonitor.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] E:\android stuin\GradleRepository\caches\transforms-3\0b3d22ab099177b3472464321ad2c41c\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] E:\android stuin\GradleRepository\caches\transforms-3\0b3d22ab099177b3472464321ad2c41c\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] E:\android stuin\GradleRepository\caches\transforms-3\e06aa37db755f95a071deab60dc4e10c\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] E:\android stuin\GradleRepository\caches\transforms-3\e06aa37db755f95a071deab60dc4e10c\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] E:\android stuin\GradleRepository\caches\transforms-3\e06aa37db755f95a071deab60dc4e10c\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
