<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2014 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<animated-selector xmlns:android="http://schemas.android.com/apk/res/android" android:constantSize="true">
    <item android:state_enabled="false" android:state_pressed="true">
        <bitmap android:src="@drawable/scrubber_control_off_mtrl_alpha" android:gravity="center" android:tint="?attr/colorControlActivated" android:alpha="?attr/disabledAlpha" />
    </item>
    <item android:state_enabled="false">
        <bitmap android:src="@drawable/scrubber_control_off_mtrl_alpha" android:gravity="center" android:tint="?attr/colorControlNormal" android:alpha="?attr/disabledAlpha" />
    </item>
    <item android:state_pressed="true" android:id="@+id/pressed">
        <bitmap android:src="@drawable/scrubber_control_to_pressed_mtrl_005" android:gravity="center" android:tint="?attr/colorControlActivated" />
    </item>
    <item android:id="@+id/not_pressed">
        <bitmap android:src="@drawable/scrubber_control_to_pressed_mtrl_000" android:gravity="center" android:tint="?attr/colorControlActivated" />
    </item>
    <transition android:fromId="@+id/not_pressed" android:toId="@+id/pressed">
        <animation-list>
            <item android:duration="15">
                <bitmap android:src="@drawable/scrubber_control_to_pressed_mtrl_000" android:gravity="center" android:tint="?attr/colorControlActivated" />
            </item>
            <item android:duration="15">
                <bitmap android:src="@drawable/scrubber_control_to_pressed_mtrl_001" android:gravity="center" android:tint="?attr/colorControlActivated" />
            </item>
            <item android:duration="15">
                <bitmap android:src="@drawable/scrubber_control_to_pressed_mtrl_002" android:gravity="center" android:tint="?attr/colorControlActivated" />
            </item>
            <item android:duration="15">
                <bitmap android:src="@drawable/scrubber_control_to_pressed_mtrl_003" android:gravity="center" android:tint="?attr/colorControlActivated" />
            </item>
            <item android:duration="15">
                <bitmap android:src="@drawable/scrubber_control_to_pressed_mtrl_004" android:gravity="center" android:tint="?attr/colorControlActivated" />
            </item>
            <item android:duration="15">
                <bitmap android:src="@drawable/scrubber_control_to_pressed_mtrl_005" android:gravity="center" android:tint="?attr/colorControlActivated" />
            </item>
        </animation-list>
    </transition>
    <transition android:fromId="@+id/pressed" android:toId="@+id/not_pressed">
        <animation-list>
            <item android:duration="15">
                <bitmap android:src="@drawable/scrubber_control_from_pressed_mtrl_000" android:gravity="center" android:tint="?attr/colorControlActivated" />
            </item>
            <item android:duration="15">
                <bitmap android:src="@drawable/scrubber_control_from_pressed_mtrl_001" android:gravity="center" android:tint="?attr/colorControlActivated" />
            </item>
            <item android:duration="15">
                <bitmap android:src="@drawable/scrubber_control_from_pressed_mtrl_002" android:gravity="center" android:tint="?attr/colorControlActivated" />
            </item>
            <item android:duration="15">
                <bitmap android:src="@drawable/scrubber_control_from_pressed_mtrl_003" android:gravity="center" android:tint="?attr/colorControlActivated" />
            </item>
            <item android:duration="15">
                <bitmap android:src="@drawable/scrubber_control_from_pressed_mtrl_004" android:gravity="center" android:tint="?attr/colorControlActivated" />
            </item>
            <item android:duration="15">
                <bitmap android:src="@drawable/scrubber_control_from_pressed_mtrl_005" android:gravity="center" android:tint="?attr/colorControlActivated" />
            </item>
        </animation-list>
    </transition>
</animated-selector>
