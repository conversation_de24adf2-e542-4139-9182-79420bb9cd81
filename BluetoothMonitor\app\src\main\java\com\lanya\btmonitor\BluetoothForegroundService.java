package com.lanya.btmonitor;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.Intent;
import android.os.Build;
import android.os.IBinder;
import androidx.core.app.NotificationCompat;

/**
 * 蓝牙监控前台服务
 * 确保应用在后台持续运行，不受电池优化影响
 */
public class BluetoothForegroundService extends Service {
    
    private static final String TAG = "BluetoothForegroundService";
    private static final int NOTIFICATION_ID = 1001;
    private static final String CHANNEL_ID = "bluetooth_monitor_channel";
    
    @Override
    public void onCreate() {
        super.onCreate();
        LogUtils.log(TAG, "前台服务创建");
        createNotificationChannel();
    }
    
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        LogUtils.log(TAG, "前台服务启动");
        
        // 创建前台通知
        Notification notification = createNotification();
        startForeground(NOTIFICATION_ID, notification);
        
        // 确保服务重启后继续运行
        return START_STICKY;
    }
    
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }
    
    @Override
    public void onDestroy() {
        LogUtils.log(TAG, "前台服务销毁");
        super.onDestroy();
    }
    
    /**
     * 创建通知渠道（Android 8.0+需要）
     */
    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                CHANNEL_ID,
                "蓝牙监控服务",
                NotificationManager.IMPORTANCE_LOW
            );
            channel.setDescription("保持蓝牙监控功能在后台运行");
            channel.setShowBadge(false);
            
            NotificationManager manager = getSystemService(NotificationManager.class);
            if (manager != null) {
                manager.createNotificationChannel(channel);
                LogUtils.log(TAG, "通知渠道已创建");
            }
        }
    }
    
    /**
     * 创建前台服务通知
     */
    private Notification createNotification() {
        return new NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("蓝牙监控运行中")
            .setContentText("正在监控附近蓝牙设备")
            .setSmallIcon(android.R.drawable.ic_dialog_info)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setOngoing(true)
            .setShowWhen(false)
            .build();
    }
    
    /**
     * 启动前台服务
     */
    public static void startService(android.content.Context context) {
        try {
            Intent serviceIntent = new Intent(context, BluetoothForegroundService.class);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(serviceIntent);
            } else {
                context.startService(serviceIntent);
            }
            LogUtils.log(TAG, "前台服务启动请求已发送");
        } catch (Exception e) {
            LogUtils.logError(TAG, "启动前台服务失败", e);
        }
    }
}
