<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:padding="18dip"
    >

    <ImageView android:id="@+id/icon"
        android:layout_centerHorizontal="true"
        android:layout_alignParentTop="true"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/usb_android" />

    <TextView android:id="@+id/banner"
        android:layout_centerHorizontal="true"
        android:layout_below="@id/icon"
        android:layout_marginTop="10dip"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="24sp"
        android:gravity="center"
        android:text="@string/usb_storage_title" />

    <TextView android:id="@+id/message"
        android:layout_below="@id/banner"
        android:layout_marginTop="10dip"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="14sp"
        android:gravity="center"
        android:text="@string/usb_storage_message" />

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="20dip"
        >

        <Button android:id="@+id/mount_button" 
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingStart="18dip"
            android:paddingEnd="18dip"
            android:text="@string/usb_storage_button_mount"
            />
        <Button android:id="@+id/unmount_button"
            android:visibility="gone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingStart="18dip"
            android:paddingEnd="18dip"
            android:text="@string/usb_storage_stop_button_mount"
            />
        <ProgressBar android:id="@+id/progress"
            android:visibility="gone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:indeterminate="true"
            style="?android:attr/progressBarStyle"
            />

    </RelativeLayout>
</RelativeLayout>
