1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.lanya.btmonitor"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="33" />
10
11    <!-- 蓝牙权限 -->
12    <uses-permission android:name="android.permission.BLUETOOTH" />
12-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:6:5-68
12-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:6:22-65
13    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
13-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:7:5-74
13-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:7:22-71
14    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
14-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:8:5-81
14-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:8:22-78
15    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
15-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:9:5-79
15-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:9:22-76
16
17    <!-- Android 12+ 蓝牙权限 -->
18    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
18-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:12:5-73
18-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:12:22-70
19    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />
19-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:13:5-78
19-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:13:22-75
20    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
20-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:14:5-76
20-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:14:22-73
21
22    <!-- 系统级权限 -->
23    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
23-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:17:5-85
23-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:17:22-82
24    <uses-permission android:name="android.permission.WAKE_LOCK" />
24-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:18:5-68
24-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:18:22-65
25
26    <!-- 网络权限 -->
27    <uses-permission android:name="android.permission.INTERNET" />
27-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:21:5-67
27-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:21:22-64
28    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
28-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:22:5-79
28-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:22:22-76
29
30    <!-- 电源管理权限 -->
31    <uses-permission android:name="android.permission.WAKE_LOCK" />
31-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:18:5-68
31-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:18:22-65
32    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
32-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:26:5-95
32-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:26:22-92
33    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
33-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:27:5-75
33-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:27:22-72
34
35    <!-- 后台运行权限 -->
36    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
36-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:30:5-77
36-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:30:22-74
37    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
37-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:31:5-78
37-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:31:22-75
38
39    <!-- Android 12+ 蓝牙权限 -->
40    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
40-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:12:5-73
40-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:12:22-70
41    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
41-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:14:5-76
41-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:14:22-73
42    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />
42-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:13:5-78
42-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:13:22-75
43
44    <permission
44-->[androidx.core:core:1.9.0] E:\android stuin\GradleRepository\caches\transforms-3\0b3d22ab099177b3472464321ad2c41c\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
45        android:name="com.lanya.btmonitor.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
45-->[androidx.core:core:1.9.0] E:\android stuin\GradleRepository\caches\transforms-3\0b3d22ab099177b3472464321ad2c41c\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
46        android:protectionLevel="signature" />
46-->[androidx.core:core:1.9.0] E:\android stuin\GradleRepository\caches\transforms-3\0b3d22ab099177b3472464321ad2c41c\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
47
48    <uses-permission android:name="com.lanya.btmonitor.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
48-->[androidx.core:core:1.9.0] E:\android stuin\GradleRepository\caches\transforms-3\0b3d22ab099177b3472464321ad2c41c\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
48-->[androidx.core:core:1.9.0] E:\android stuin\GradleRepository\caches\transforms-3\0b3d22ab099177b3472464321ad2c41c\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
49
50    <application
50-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:38:5-67:19
51        android:allowBackup="true"
51-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:39:9-35
52        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
52-->[androidx.core:core:1.9.0] E:\android stuin\GradleRepository\caches\transforms-3\0b3d22ab099177b3472464321ad2c41c\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
53        android:dataExtractionRules="@xml/data_extraction_rules"
53-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:40:9-65
54        android:debuggable="true"
55        android:extractNativeLibs="true"
56        android:fullBackupContent="@xml/backup_rules"
56-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:41:9-54
57        android:icon="@mipmap/ic_launcher"
57-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:42:9-43
58        android:label="@string/app_name"
58-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:43:9-41
59        android:roundIcon="@mipmap/ic_launcher_round"
59-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:44:9-54
60        android:supportsRtl="true"
60-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:45:9-35
61        android:theme="@style/Theme.BluetoothMonitor" >
61-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:46:9-54
62
63        <!-- LSPosed 模块声明 -->
64        <meta-data
64-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:50:9-52:36
65            android:name="xposedmodule"
65-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:51:13-40
66            android:value="true" />
66-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:52:13-33
67        <meta-data
67-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:53:9-55:50
68            android:name="xposeddescription"
68-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:54:13-45
69            android:value="实时监控附近蓝牙设备名称和RSSI值" />
69-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:55:13-47
70        <meta-data
70-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:56:9-58:35
71            android:name="xposedminversion"
71-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:57:13-44
72            android:value="100" />
72-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:58:13-32
73
74        <!-- 蓝牙监控前台服务 -->
75        <service
75-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:61:9-65:63
76            android:name="com.lanya.btmonitor.BluetoothForegroundService"
76-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:62:13-55
77            android:enabled="true"
77-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:63:13-35
78            android:exported="false"
78-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:64:13-37
79            android:foregroundServiceType="connectedDevice" />
79-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:65:13-60
80
81        <provider
81-->[androidx.emoji2:emoji2:1.2.0] E:\android stuin\GradleRepository\caches\transforms-3\9e497d5e9f54b6e90ad951dbab4c9ece\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
82            android:name="androidx.startup.InitializationProvider"
82-->[androidx.emoji2:emoji2:1.2.0] E:\android stuin\GradleRepository\caches\transforms-3\9e497d5e9f54b6e90ad951dbab4c9ece\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
83            android:authorities="com.lanya.btmonitor.androidx-startup"
83-->[androidx.emoji2:emoji2:1.2.0] E:\android stuin\GradleRepository\caches\transforms-3\9e497d5e9f54b6e90ad951dbab4c9ece\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
84            android:exported="false" >
84-->[androidx.emoji2:emoji2:1.2.0] E:\android stuin\GradleRepository\caches\transforms-3\9e497d5e9f54b6e90ad951dbab4c9ece\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
85            <meta-data
85-->[androidx.emoji2:emoji2:1.2.0] E:\android stuin\GradleRepository\caches\transforms-3\9e497d5e9f54b6e90ad951dbab4c9ece\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
86                android:name="androidx.emoji2.text.EmojiCompatInitializer"
86-->[androidx.emoji2:emoji2:1.2.0] E:\android stuin\GradleRepository\caches\transforms-3\9e497d5e9f54b6e90ad951dbab4c9ece\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
87                android:value="androidx.startup" />
87-->[androidx.emoji2:emoji2:1.2.0] E:\android stuin\GradleRepository\caches\transforms-3\9e497d5e9f54b6e90ad951dbab4c9ece\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
88            <meta-data
88-->[androidx.lifecycle:lifecycle-process:2.4.1] E:\android stuin\GradleRepository\caches\transforms-3\e06aa37db755f95a071deab60dc4e10c\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
89                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
89-->[androidx.lifecycle:lifecycle-process:2.4.1] E:\android stuin\GradleRepository\caches\transforms-3\e06aa37db755f95a071deab60dc4e10c\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
90                android:value="androidx.startup" />
90-->[androidx.lifecycle:lifecycle-process:2.4.1] E:\android stuin\GradleRepository\caches\transforms-3\e06aa37db755f95a071deab60dc4e10c\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
91        </provider>
92    </application>
93
94</manifest>
