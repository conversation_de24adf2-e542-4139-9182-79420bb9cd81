1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.lanya.btmonitor"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="33" />
10
11    <!-- 基础蓝牙权限 -->
12    <uses-permission android:name="android.permission.BLUETOOTH" />
12-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:6:5-68
12-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:6:22-65
13    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
13-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:7:5-74
13-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:7:22-71
14    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
14-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:8:5-81
14-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:8:22-78
15    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
15-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:9:5-79
15-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:9:22-76
16    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
16-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:10:5-85
16-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:10:22-82
17
18    <!-- Android 12+ 蓝牙权限 -->
19    <uses-permission
19-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:13:5-14:58
20        android:name="android.permission.BLUETOOTH_SCAN"
20-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:13:22-70
21        android:usesPermissionFlags="neverForLocation" />
21-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:14:9-55
22    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />
22-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:15:5-78
22-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:15:22-75
23    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
23-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:16:5-76
23-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:16:22-73
24
25    <!-- 网络权限 -->
26    <uses-permission android:name="android.permission.INTERNET" />
26-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:19:5-67
26-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:19:22-64
27    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
27-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:20:5-79
27-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:20:22-76
28
29    <!-- 电源管理权限 -->
30    <uses-permission android:name="android.permission.WAKE_LOCK" />
30-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:23:5-68
30-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:23:22-65
31    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
31-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:24:5-95
31-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:24:22-92
32    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
32-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:25:5-75
32-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:25:22-72
33
34    <!-- 后台运行权限 -->
35    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
35-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:28:5-77
35-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:28:22-74
36    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_CONNECTED_DEVICE" />
36-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:29:5-94
36-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:29:22-91
37    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
37-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:30:5-78
37-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:30:22-75
38
39    <!-- Android 15 新增权限 -->
40    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
40-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:33:5-77
40-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:33:22-74
41    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />
41-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:34:5-81
41-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:34:22-78
42
43    <!-- 小米设备特殊权限 -->
44    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
44-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:37:5-81
44-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:37:22-78
45    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
45-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:38:5-39:53
45-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:38:22-74
46
47    <permission
47-->[androidx.core:core:1.9.0] E:\android stuin\GradleRepository\caches\transforms-3\0b3d22ab099177b3472464321ad2c41c\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
48        android:name="com.lanya.btmonitor.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
48-->[androidx.core:core:1.9.0] E:\android stuin\GradleRepository\caches\transforms-3\0b3d22ab099177b3472464321ad2c41c\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
49        android:protectionLevel="signature" />
49-->[androidx.core:core:1.9.0] E:\android stuin\GradleRepository\caches\transforms-3\0b3d22ab099177b3472464321ad2c41c\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
50
51    <uses-permission android:name="com.lanya.btmonitor.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
51-->[androidx.core:core:1.9.0] E:\android stuin\GradleRepository\caches\transforms-3\0b3d22ab099177b3472464321ad2c41c\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
51-->[androidx.core:core:1.9.0] E:\android stuin\GradleRepository\caches\transforms-3\0b3d22ab099177b3472464321ad2c41c\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
52
53    <application
53-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:41:5-70:19
54        android:allowBackup="true"
54-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:42:9-35
55        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
55-->[androidx.core:core:1.9.0] E:\android stuin\GradleRepository\caches\transforms-3\0b3d22ab099177b3472464321ad2c41c\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
56        android:dataExtractionRules="@xml/data_extraction_rules"
56-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:43:9-65
57        android:debuggable="true"
58        android:extractNativeLibs="true"
59        android:fullBackupContent="@xml/backup_rules"
59-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:44:9-54
60        android:icon="@mipmap/ic_launcher"
60-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:45:9-43
61        android:label="@string/app_name"
61-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:46:9-41
62        android:roundIcon="@mipmap/ic_launcher_round"
62-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:47:9-54
63        android:supportsRtl="true"
63-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:48:9-35
64        android:theme="@style/Theme.BluetoothMonitor" >
64-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:49:9-54
65
66        <!-- LSPosed 模块声明 -->
67        <meta-data
67-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:53:9-55:36
68            android:name="xposedmodule"
68-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:54:13-40
69            android:value="true" />
69-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:55:13-33
70        <meta-data
70-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:56:9-58:50
71            android:name="xposeddescription"
71-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:57:13-45
72            android:value="实时监控附近蓝牙设备名称和RSSI值" />
72-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:58:13-47
73        <meta-data
73-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:59:9-61:35
74            android:name="xposedminversion"
74-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:60:13-44
75            android:value="100" />
75-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:61:13-32
76
77        <!-- 蓝牙监控前台服务 -->
78        <service
78-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:64:9-68:63
79            android:name="com.lanya.btmonitor.BluetoothForegroundService"
79-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:65:13-55
80            android:enabled="true"
80-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:66:13-35
81            android:exported="false"
81-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:67:13-37
82            android:foregroundServiceType="connectedDevice" />
82-->C:\Users\<USER>\Desktop\Cursor\lanya\BluetoothMonitor\app\src\main\AndroidManifest.xml:68:13-60
83
84        <provider
84-->[androidx.emoji2:emoji2:1.2.0] E:\android stuin\GradleRepository\caches\transforms-3\9e497d5e9f54b6e90ad951dbab4c9ece\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
85            android:name="androidx.startup.InitializationProvider"
85-->[androidx.emoji2:emoji2:1.2.0] E:\android stuin\GradleRepository\caches\transforms-3\9e497d5e9f54b6e90ad951dbab4c9ece\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
86            android:authorities="com.lanya.btmonitor.androidx-startup"
86-->[androidx.emoji2:emoji2:1.2.0] E:\android stuin\GradleRepository\caches\transforms-3\9e497d5e9f54b6e90ad951dbab4c9ece\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
87            android:exported="false" >
87-->[androidx.emoji2:emoji2:1.2.0] E:\android stuin\GradleRepository\caches\transforms-3\9e497d5e9f54b6e90ad951dbab4c9ece\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
88            <meta-data
88-->[androidx.emoji2:emoji2:1.2.0] E:\android stuin\GradleRepository\caches\transforms-3\9e497d5e9f54b6e90ad951dbab4c9ece\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
89                android:name="androidx.emoji2.text.EmojiCompatInitializer"
89-->[androidx.emoji2:emoji2:1.2.0] E:\android stuin\GradleRepository\caches\transforms-3\9e497d5e9f54b6e90ad951dbab4c9ece\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
90                android:value="androidx.startup" />
90-->[androidx.emoji2:emoji2:1.2.0] E:\android stuin\GradleRepository\caches\transforms-3\9e497d5e9f54b6e90ad951dbab4c9ece\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
91            <meta-data
91-->[androidx.lifecycle:lifecycle-process:2.4.1] E:\android stuin\GradleRepository\caches\transforms-3\e06aa37db755f95a071deab60dc4e10c\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
92                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
92-->[androidx.lifecycle:lifecycle-process:2.4.1] E:\android stuin\GradleRepository\caches\transforms-3\e06aa37db755f95a071deab60dc4e10c\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
93                android:value="androidx.startup" />
93-->[androidx.lifecycle:lifecycle-process:2.4.1] E:\android stuin\GradleRepository\caches\transforms-3\e06aa37db755f95a071deab60dc4e10c\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
94        </provider>
95    </application>
96
97</manifest>
