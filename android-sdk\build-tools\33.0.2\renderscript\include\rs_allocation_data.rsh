/*
 * Copyright (C) 2016 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// Don't edit this file!  It is auto-generated by frameworks/rs/api/generate.sh.

/*
 * rs_allocation_data.rsh: Allocation Data Access Functions
 *
 * The functions below can be used to get and set the cells that comprise
 * an allocation.
 *
 * - Individual cells are accessed using the rsGetElementAt* and
 *   rsSetElementAt functions.
 * - Multiple cells can be copied using the rsAllocationCopy* and
 *   rsAllocationV* functions.
 * - For getting values through a sampler, use rsSample.
 *
 * The rsGetElementAt and rsSetElement* functions are somewhat misnamed.
 * They don't get or set elements, which are akin to data types; they get
 * or set cells.  Think of them as rsGetCellAt and and rsSetCellAt.
 */

#ifndef RENDERSCRIPT_RS_ALLOCATION_DATA_RSH
#define RENDERSCRIPT_RS_ALLOCATION_DATA_RSH

/*
 * rsAllocationCopy1DRange: Copy consecutive cells between allocations
 *
 * Copies the specified number of cells from one allocation to another.
 *
 * The two allocations must be different.  Using this function to copy whithin
 * the same allocation yields undefined results.
 *
 * The function does not validate whether the offset plus count exceeds the size
 * of either allocation.  Be careful!
 *
 * This function should only be called between 1D allocations.  Calling it
 * on other allocations is undefined.
 *
 * This function should not be called from inside a kernel, or from any function
 * that may be called directly or indirectly from a kernel. Doing so would cause a
 * runtime error.
 *
 * Parameters:
 *   dstAlloc: Allocation to copy cells into.
 *   dstOff: Offset in the destination of the first cell to be copied into.
 *   dstMip: Mip level in the destination allocation.  0 if mip mapping is not used.
 *   count: Number of cells to be copied.
 *   srcAlloc: Source allocation.
 *   srcOff: Offset in the source of the first cell to be copied.
 *   srcMip: Mip level in the source allocation.  0 if mip mapping is not used.
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 14))
extern void __attribute__((overloadable))
    rsAllocationCopy1DRange(rs_allocation dstAlloc, uint32_t dstOff, uint32_t dstMip, uint32_t count,
                            rs_allocation srcAlloc, uint32_t srcOff, uint32_t srcMip);
#endif

/*
 * rsAllocationCopy2DRange: Copy a rectangular region of cells between allocations
 *
 * Copies a rectangular region of cells from one allocation to another.
 * (width * heigth) cells are copied.
 *
 * The two allocations must be different.  Using this function to copy whithin
 * the same allocation yields undefined results.
 *
 * The function does not validate whether the the source or destination region
 * exceeds the size of its respective allocation.  Be careful!
 *
 * This function should only be called between 2D allocations.  Calling it
 * on other allocations is undefined.
 *
 * This function should not be called from inside a kernel, or from any function
 * that may be called directly or indirectly from a kernel. Doing so would cause a
 * runtime error.
 *
 * Parameters:
 *   dstAlloc: Allocation to copy cells into.
 *   dstXoff: X offset in the destination of the region to be set.
 *   dstYoff: Y offset in the destination of the region to be set.
 *   dstMip: Mip level in the destination allocation.  0 if mip mapping is not used.
 *   dstFace: Cubemap face of the destination allocation.  Ignored for allocations that aren't cubemaps.
 *   width: Width of the incoming region to update.
 *   height: Height of the incoming region to update.
 *   srcAlloc: Source allocation.
 *   srcXoff: X offset in the source.
 *   srcYoff: Y offset in the source.
 *   srcMip: Mip level in the source allocation.  0 if mip mapping is not used.
 *   srcFace: Cubemap face of the source allocation.  Ignored for allocations that aren't cubemaps.
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 14))
extern void __attribute__((overloadable))
    rsAllocationCopy2DRange(rs_allocation dstAlloc, uint32_t dstXoff, uint32_t dstYoff,
                            uint32_t dstMip, rs_allocation_cubemap_face dstFace, uint32_t width,
                            uint32_t height, rs_allocation srcAlloc, uint32_t srcXoff,
                            uint32_t srcYoff, uint32_t srcMip, rs_allocation_cubemap_face srcFace);
#endif

/*
 * rsAllocationVLoadX: Get a vector from an allocation of scalars
 *
 * This function returns a vector composed of successive cells of the allocation.
 * It assumes that the allocation contains scalars.
 *
 * The "X" in the name indicates that successive values are extracted by
 * increasing the X index.  There are currently no functions to get successive
 * values incrementing other dimensions.  Use multiple calls to rsGetElementAt()
 * instead.
 *
 * For example, when calling rsAllocationVLoadX_int4(a, 20, 30), an int4 composed
 * of a[20, 30], a[21, 30], a[22, 30], and a[23, 30] is returned.
 *
 * When retrieving from a three dimensional allocations, use the x, y, z variant.
 * Similarly, use the x, y variant for two dimensional allocations and x for the
 * mono dimensional allocations.
 *
 * For efficiency, this function does not validate the inputs.  Trying to wrap
 * the X index, exceeding the size of the allocation, or using indices incompatible
 * with the dimensionality of the allocation yields undefined results.
 *
 * See also rsAllocationVStoreX().
 *
 * Parameters:
 *   a: Allocation to get the data from.
 *   x: X offset in the allocation of the first cell to be copied from.
 *   y: Y offset in the allocation of the first cell to be copied from.
 *   z: Z offset in the allocation of the first cell to be copied from.
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern float2 __attribute__((overloadable))
    rsAllocationVLoadX_float2(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern float3 __attribute__((overloadable))
    rsAllocationVLoadX_float3(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern float4 __attribute__((overloadable))
    rsAllocationVLoadX_float4(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern double2 __attribute__((overloadable))
    rsAllocationVLoadX_double2(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern double3 __attribute__((overloadable))
    rsAllocationVLoadX_double3(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern double4 __attribute__((overloadable))
    rsAllocationVLoadX_double4(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern char2 __attribute__((overloadable))
    rsAllocationVLoadX_char2(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern char3 __attribute__((overloadable))
    rsAllocationVLoadX_char3(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern char4 __attribute__((overloadable))
    rsAllocationVLoadX_char4(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern uchar2 __attribute__((overloadable))
    rsAllocationVLoadX_uchar2(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern uchar3 __attribute__((overloadable))
    rsAllocationVLoadX_uchar3(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern uchar4 __attribute__((overloadable))
    rsAllocationVLoadX_uchar4(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern short2 __attribute__((overloadable))
    rsAllocationVLoadX_short2(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern short3 __attribute__((overloadable))
    rsAllocationVLoadX_short3(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern short4 __attribute__((overloadable))
    rsAllocationVLoadX_short4(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern ushort2 __attribute__((overloadable))
    rsAllocationVLoadX_ushort2(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern ushort3 __attribute__((overloadable))
    rsAllocationVLoadX_ushort3(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern ushort4 __attribute__((overloadable))
    rsAllocationVLoadX_ushort4(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern int2 __attribute__((overloadable))
    rsAllocationVLoadX_int2(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern int3 __attribute__((overloadable))
    rsAllocationVLoadX_int3(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern int4 __attribute__((overloadable))
    rsAllocationVLoadX_int4(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern uint2 __attribute__((overloadable))
    rsAllocationVLoadX_uint2(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern uint3 __attribute__((overloadable))
    rsAllocationVLoadX_uint3(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern uint4 __attribute__((overloadable))
    rsAllocationVLoadX_uint4(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern long2 __attribute__((overloadable))
    rsAllocationVLoadX_long2(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern long3 __attribute__((overloadable))
    rsAllocationVLoadX_long3(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern long4 __attribute__((overloadable))
    rsAllocationVLoadX_long4(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern ulong2 __attribute__((overloadable))
    rsAllocationVLoadX_ulong2(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern ulong3 __attribute__((overloadable))
    rsAllocationVLoadX_ulong3(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern ulong4 __attribute__((overloadable))
    rsAllocationVLoadX_ulong4(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern float2 __attribute__((overloadable))
    rsAllocationVLoadX_float2(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern float3 __attribute__((overloadable))
    rsAllocationVLoadX_float3(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern float4 __attribute__((overloadable))
    rsAllocationVLoadX_float4(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern double2 __attribute__((overloadable))
    rsAllocationVLoadX_double2(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern double3 __attribute__((overloadable))
    rsAllocationVLoadX_double3(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern double4 __attribute__((overloadable))
    rsAllocationVLoadX_double4(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern char2 __attribute__((overloadable))
    rsAllocationVLoadX_char2(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern char3 __attribute__((overloadable))
    rsAllocationVLoadX_char3(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern char4 __attribute__((overloadable))
    rsAllocationVLoadX_char4(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern uchar2 __attribute__((overloadable))
    rsAllocationVLoadX_uchar2(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern uchar3 __attribute__((overloadable))
    rsAllocationVLoadX_uchar3(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern uchar4 __attribute__((overloadable))
    rsAllocationVLoadX_uchar4(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern short2 __attribute__((overloadable))
    rsAllocationVLoadX_short2(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern short3 __attribute__((overloadable))
    rsAllocationVLoadX_short3(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern short4 __attribute__((overloadable))
    rsAllocationVLoadX_short4(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern ushort2 __attribute__((overloadable))
    rsAllocationVLoadX_ushort2(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern ushort3 __attribute__((overloadable))
    rsAllocationVLoadX_ushort3(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern ushort4 __attribute__((overloadable))
    rsAllocationVLoadX_ushort4(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern int2 __attribute__((overloadable))
    rsAllocationVLoadX_int2(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern int3 __attribute__((overloadable))
    rsAllocationVLoadX_int3(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern int4 __attribute__((overloadable))
    rsAllocationVLoadX_int4(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern uint2 __attribute__((overloadable))
    rsAllocationVLoadX_uint2(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern uint3 __attribute__((overloadable))
    rsAllocationVLoadX_uint3(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern uint4 __attribute__((overloadable))
    rsAllocationVLoadX_uint4(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern long2 __attribute__((overloadable))
    rsAllocationVLoadX_long2(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern long3 __attribute__((overloadable))
    rsAllocationVLoadX_long3(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern long4 __attribute__((overloadable))
    rsAllocationVLoadX_long4(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern ulong2 __attribute__((overloadable))
    rsAllocationVLoadX_ulong2(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern ulong3 __attribute__((overloadable))
    rsAllocationVLoadX_ulong3(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern ulong4 __attribute__((overloadable))
    rsAllocationVLoadX_ulong4(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern float2 __attribute__((overloadable))
    rsAllocationVLoadX_float2(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern float3 __attribute__((overloadable))
    rsAllocationVLoadX_float3(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern float4 __attribute__((overloadable))
    rsAllocationVLoadX_float4(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern double2 __attribute__((overloadable))
    rsAllocationVLoadX_double2(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern double3 __attribute__((overloadable))
    rsAllocationVLoadX_double3(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern double4 __attribute__((overloadable))
    rsAllocationVLoadX_double4(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern char2 __attribute__((overloadable))
    rsAllocationVLoadX_char2(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern char3 __attribute__((overloadable))
    rsAllocationVLoadX_char3(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern char4 __attribute__((overloadable))
    rsAllocationVLoadX_char4(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern uchar2 __attribute__((overloadable))
    rsAllocationVLoadX_uchar2(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern uchar3 __attribute__((overloadable))
    rsAllocationVLoadX_uchar3(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern uchar4 __attribute__((overloadable))
    rsAllocationVLoadX_uchar4(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern short2 __attribute__((overloadable))
    rsAllocationVLoadX_short2(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern short3 __attribute__((overloadable))
    rsAllocationVLoadX_short3(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern short4 __attribute__((overloadable))
    rsAllocationVLoadX_short4(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern ushort2 __attribute__((overloadable))
    rsAllocationVLoadX_ushort2(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern ushort3 __attribute__((overloadable))
    rsAllocationVLoadX_ushort3(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern ushort4 __attribute__((overloadable))
    rsAllocationVLoadX_ushort4(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern int2 __attribute__((overloadable))
    rsAllocationVLoadX_int2(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern int3 __attribute__((overloadable))
    rsAllocationVLoadX_int3(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern int4 __attribute__((overloadable))
    rsAllocationVLoadX_int4(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern uint2 __attribute__((overloadable))
    rsAllocationVLoadX_uint2(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern uint3 __attribute__((overloadable))
    rsAllocationVLoadX_uint3(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern uint4 __attribute__((overloadable))
    rsAllocationVLoadX_uint4(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern long2 __attribute__((overloadable))
    rsAllocationVLoadX_long2(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern long3 __attribute__((overloadable))
    rsAllocationVLoadX_long3(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern long4 __attribute__((overloadable))
    rsAllocationVLoadX_long4(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern ulong2 __attribute__((overloadable))
    rsAllocationVLoadX_ulong2(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern ulong3 __attribute__((overloadable))
    rsAllocationVLoadX_ulong3(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern ulong4 __attribute__((overloadable))
    rsAllocationVLoadX_ulong4(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

/*
 * rsAllocationVStoreX: Store a vector into an allocation of scalars
 *
 * This function stores the entries of a vector into successive cells of an allocation.
 * It assumes that the allocation contains scalars.
 *
 * The "X" in the name indicates that successive values are stored by increasing
 * the X index.  There are currently no functions to store successive values
 * incrementing other dimensions.  Use multiple calls to rsSetElementAt() instead.
 *
 * For example, when calling rsAllocationVStoreX_int3(a, v, 20, 30), v.x is stored
 * at a[20, 30], v.y at a[21, 30], and v.z at a[22, 30].
 *
 * When storing into a three dimensional allocations, use the x, y, z variant.
 * Similarly, use the x, y variant for two dimensional allocations and x for the
 * mono dimensional allocations.
 *
 * For efficiency, this function does not validate the inputs.  Trying to wrap the
 * X index, exceeding the size of the allocation, or using indices incompatible
 * with the dimensionality of the allocation yiels undefined results.
 *
 * See also rsAllocationVLoadX().
 *
 * Parameters:
 *   a: Allocation to store the data into.
 *   val: Value to be stored.
 *   x: X offset in the allocation of the first cell to be copied into.
 *   y: Y offset in the allocation of the first cell to be copied into.
 *   z: Z offset in the allocation of the first cell to be copied into.
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_float2(rs_allocation a, float2 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_float3(rs_allocation a, float3 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_float4(rs_allocation a, float4 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_double2(rs_allocation a, double2 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_double3(rs_allocation a, double3 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_double4(rs_allocation a, double4 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_char2(rs_allocation a, char2 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_char3(rs_allocation a, char3 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_char4(rs_allocation a, char4 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_uchar2(rs_allocation a, uchar2 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_uchar3(rs_allocation a, uchar3 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_uchar4(rs_allocation a, uchar4 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_short2(rs_allocation a, short2 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_short3(rs_allocation a, short3 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_short4(rs_allocation a, short4 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_ushort2(rs_allocation a, ushort2 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_ushort3(rs_allocation a, ushort3 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_ushort4(rs_allocation a, ushort4 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_int2(rs_allocation a, int2 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_int3(rs_allocation a, int3 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_int4(rs_allocation a, int4 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_uint2(rs_allocation a, uint2 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_uint3(rs_allocation a, uint3 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_uint4(rs_allocation a, uint4 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_long2(rs_allocation a, long2 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_long3(rs_allocation a, long3 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_long4(rs_allocation a, long4 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_ulong2(rs_allocation a, ulong2 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_ulong3(rs_allocation a, ulong3 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_ulong4(rs_allocation a, ulong4 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_float2(rs_allocation a, float2 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_float3(rs_allocation a, float3 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_float4(rs_allocation a, float4 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_double2(rs_allocation a, double2 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_double3(rs_allocation a, double3 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_double4(rs_allocation a, double4 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_char2(rs_allocation a, char2 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_char3(rs_allocation a, char3 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_char4(rs_allocation a, char4 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_uchar2(rs_allocation a, uchar2 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_uchar3(rs_allocation a, uchar3 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_uchar4(rs_allocation a, uchar4 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_short2(rs_allocation a, short2 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_short3(rs_allocation a, short3 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_short4(rs_allocation a, short4 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_ushort2(rs_allocation a, ushort2 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_ushort3(rs_allocation a, ushort3 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_ushort4(rs_allocation a, ushort4 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_int2(rs_allocation a, int2 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_int3(rs_allocation a, int3 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_int4(rs_allocation a, int4 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_uint2(rs_allocation a, uint2 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_uint3(rs_allocation a, uint3 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_uint4(rs_allocation a, uint4 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_long2(rs_allocation a, long2 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_long3(rs_allocation a, long3 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_long4(rs_allocation a, long4 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_ulong2(rs_allocation a, ulong2 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_ulong3(rs_allocation a, ulong3 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_ulong4(rs_allocation a, ulong4 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_float2(rs_allocation a, float2 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_float3(rs_allocation a, float3 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_float4(rs_allocation a, float4 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_double2(rs_allocation a, double2 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_double3(rs_allocation a, double3 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_double4(rs_allocation a, double4 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_char2(rs_allocation a, char2 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_char3(rs_allocation a, char3 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_char4(rs_allocation a, char4 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_uchar2(rs_allocation a, uchar2 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_uchar3(rs_allocation a, uchar3 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_uchar4(rs_allocation a, uchar4 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_short2(rs_allocation a, short2 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_short3(rs_allocation a, short3 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_short4(rs_allocation a, short4 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_ushort2(rs_allocation a, ushort2 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_ushort3(rs_allocation a, ushort3 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_ushort4(rs_allocation a, ushort4 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_int2(rs_allocation a, int2 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_int3(rs_allocation a, int3 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_int4(rs_allocation a, int4 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_uint2(rs_allocation a, uint2 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_uint3(rs_allocation a, uint3 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_uint4(rs_allocation a, uint4 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_long2(rs_allocation a, long2 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_long3(rs_allocation a, long3 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_long4(rs_allocation a, long4 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_ulong2(rs_allocation a, ulong2 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_ulong3(rs_allocation a, ulong3 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 22))
extern void __attribute__((overloadable))
    rsAllocationVStoreX_ulong4(rs_allocation a, ulong4 val, uint32_t x, uint32_t y, uint32_t z);
#endif

/*
 * rsGetElementAt: Return a cell from an allocation
 *
 * This function extracts a single cell from an allocation.
 *
 * When retrieving from a three dimensional allocations, use the x, y, z variant.
 * Similarly, use the x, y variant for two dimensional allocations and x for the
 * mono dimensional allocations.
 *
 * This function has two styles.  One returns the address of the value using a void*,
 * the other returns the actual value, e.g. rsGetElementAt() vs. rsGetElementAt_int4().
 * For primitive types, always use the latter as it is more efficient.
 */
extern const void* __attribute__((overloadable))
    rsGetElementAt(rs_allocation a, uint32_t x);

extern const void* __attribute__((overloadable))
    rsGetElementAt(rs_allocation a, uint32_t x, uint32_t y);

extern const void* __attribute__((overloadable))
    rsGetElementAt(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline float __attribute__((overloadable))
    rsGetElementAt_float(rs_allocation a, uint32_t x) {
    return ((float *)rsGetElementAt(a, x))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline float2 __attribute__((overloadable))
    rsGetElementAt_float2(rs_allocation a, uint32_t x) {
    return ((float2 *)rsGetElementAt(a, x))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline float3 __attribute__((overloadable))
    rsGetElementAt_float3(rs_allocation a, uint32_t x) {
    return ((float3 *)rsGetElementAt(a, x))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline float4 __attribute__((overloadable))
    rsGetElementAt_float4(rs_allocation a, uint32_t x) {
    return ((float4 *)rsGetElementAt(a, x))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline double __attribute__((overloadable))
    rsGetElementAt_double(rs_allocation a, uint32_t x) {
    return ((double *)rsGetElementAt(a, x))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline double2 __attribute__((overloadable))
    rsGetElementAt_double2(rs_allocation a, uint32_t x) {
    return ((double2 *)rsGetElementAt(a, x))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline double3 __attribute__((overloadable))
    rsGetElementAt_double3(rs_allocation a, uint32_t x) {
    return ((double3 *)rsGetElementAt(a, x))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline double4 __attribute__((overloadable))
    rsGetElementAt_double4(rs_allocation a, uint32_t x) {
    return ((double4 *)rsGetElementAt(a, x))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline char __attribute__((overloadable))
    rsGetElementAt_char(rs_allocation a, uint32_t x) {
    return ((char *)rsGetElementAt(a, x))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline char2 __attribute__((overloadable))
    rsGetElementAt_char2(rs_allocation a, uint32_t x) {
    return ((char2 *)rsGetElementAt(a, x))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline char3 __attribute__((overloadable))
    rsGetElementAt_char3(rs_allocation a, uint32_t x) {
    return ((char3 *)rsGetElementAt(a, x))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline char4 __attribute__((overloadable))
    rsGetElementAt_char4(rs_allocation a, uint32_t x) {
    return ((char4 *)rsGetElementAt(a, x))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline uchar __attribute__((overloadable))
    rsGetElementAt_uchar(rs_allocation a, uint32_t x) {
    return ((uchar *)rsGetElementAt(a, x))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline uchar2 __attribute__((overloadable))
    rsGetElementAt_uchar2(rs_allocation a, uint32_t x) {
    return ((uchar2 *)rsGetElementAt(a, x))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline uchar3 __attribute__((overloadable))
    rsGetElementAt_uchar3(rs_allocation a, uint32_t x) {
    return ((uchar3 *)rsGetElementAt(a, x))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline uchar4 __attribute__((overloadable))
    rsGetElementAt_uchar4(rs_allocation a, uint32_t x) {
    return ((uchar4 *)rsGetElementAt(a, x))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline short __attribute__((overloadable))
    rsGetElementAt_short(rs_allocation a, uint32_t x) {
    return ((short *)rsGetElementAt(a, x))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline short2 __attribute__((overloadable))
    rsGetElementAt_short2(rs_allocation a, uint32_t x) {
    return ((short2 *)rsGetElementAt(a, x))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline short3 __attribute__((overloadable))
    rsGetElementAt_short3(rs_allocation a, uint32_t x) {
    return ((short3 *)rsGetElementAt(a, x))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline short4 __attribute__((overloadable))
    rsGetElementAt_short4(rs_allocation a, uint32_t x) {
    return ((short4 *)rsGetElementAt(a, x))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline ushort __attribute__((overloadable))
    rsGetElementAt_ushort(rs_allocation a, uint32_t x) {
    return ((ushort *)rsGetElementAt(a, x))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline ushort2 __attribute__((overloadable))
    rsGetElementAt_ushort2(rs_allocation a, uint32_t x) {
    return ((ushort2 *)rsGetElementAt(a, x))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline ushort3 __attribute__((overloadable))
    rsGetElementAt_ushort3(rs_allocation a, uint32_t x) {
    return ((ushort3 *)rsGetElementAt(a, x))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline ushort4 __attribute__((overloadable))
    rsGetElementAt_ushort4(rs_allocation a, uint32_t x) {
    return ((ushort4 *)rsGetElementAt(a, x))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline int __attribute__((overloadable))
    rsGetElementAt_int(rs_allocation a, uint32_t x) {
    return ((int *)rsGetElementAt(a, x))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline int2 __attribute__((overloadable))
    rsGetElementAt_int2(rs_allocation a, uint32_t x) {
    return ((int2 *)rsGetElementAt(a, x))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline int3 __attribute__((overloadable))
    rsGetElementAt_int3(rs_allocation a, uint32_t x) {
    return ((int3 *)rsGetElementAt(a, x))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline int4 __attribute__((overloadable))
    rsGetElementAt_int4(rs_allocation a, uint32_t x) {
    return ((int4 *)rsGetElementAt(a, x))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline uint __attribute__((overloadable))
    rsGetElementAt_uint(rs_allocation a, uint32_t x) {
    return ((uint *)rsGetElementAt(a, x))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline uint2 __attribute__((overloadable))
    rsGetElementAt_uint2(rs_allocation a, uint32_t x) {
    return ((uint2 *)rsGetElementAt(a, x))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline uint3 __attribute__((overloadable))
    rsGetElementAt_uint3(rs_allocation a, uint32_t x) {
    return ((uint3 *)rsGetElementAt(a, x))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline uint4 __attribute__((overloadable))
    rsGetElementAt_uint4(rs_allocation a, uint32_t x) {
    return ((uint4 *)rsGetElementAt(a, x))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline long __attribute__((overloadable))
    rsGetElementAt_long(rs_allocation a, uint32_t x) {
    return ((long *)rsGetElementAt(a, x))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline long2 __attribute__((overloadable))
    rsGetElementAt_long2(rs_allocation a, uint32_t x) {
    return ((long2 *)rsGetElementAt(a, x))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline long3 __attribute__((overloadable))
    rsGetElementAt_long3(rs_allocation a, uint32_t x) {
    return ((long3 *)rsGetElementAt(a, x))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline long4 __attribute__((overloadable))
    rsGetElementAt_long4(rs_allocation a, uint32_t x) {
    return ((long4 *)rsGetElementAt(a, x))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline ulong __attribute__((overloadable))
    rsGetElementAt_ulong(rs_allocation a, uint32_t x) {
    return ((ulong *)rsGetElementAt(a, x))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline ulong2 __attribute__((overloadable))
    rsGetElementAt_ulong2(rs_allocation a, uint32_t x) {
    return ((ulong2 *)rsGetElementAt(a, x))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline ulong3 __attribute__((overloadable))
    rsGetElementAt_ulong3(rs_allocation a, uint32_t x) {
    return ((ulong3 *)rsGetElementAt(a, x))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline ulong4 __attribute__((overloadable))
    rsGetElementAt_ulong4(rs_allocation a, uint32_t x) {
    return ((ulong4 *)rsGetElementAt(a, x))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline float __attribute__((overloadable))
    rsGetElementAt_float(rs_allocation a, uint32_t x, uint32_t y) {
    return ((float *)rsGetElementAt(a, x, y))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline float2 __attribute__((overloadable))
    rsGetElementAt_float2(rs_allocation a, uint32_t x, uint32_t y) {
    return ((float2 *)rsGetElementAt(a, x, y))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline float3 __attribute__((overloadable))
    rsGetElementAt_float3(rs_allocation a, uint32_t x, uint32_t y) {
    return ((float3 *)rsGetElementAt(a, x, y))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline float4 __attribute__((overloadable))
    rsGetElementAt_float4(rs_allocation a, uint32_t x, uint32_t y) {
    return ((float4 *)rsGetElementAt(a, x, y))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline double __attribute__((overloadable))
    rsGetElementAt_double(rs_allocation a, uint32_t x, uint32_t y) {
    return ((double *)rsGetElementAt(a, x, y))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline double2 __attribute__((overloadable))
    rsGetElementAt_double2(rs_allocation a, uint32_t x, uint32_t y) {
    return ((double2 *)rsGetElementAt(a, x, y))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline double3 __attribute__((overloadable))
    rsGetElementAt_double3(rs_allocation a, uint32_t x, uint32_t y) {
    return ((double3 *)rsGetElementAt(a, x, y))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline double4 __attribute__((overloadable))
    rsGetElementAt_double4(rs_allocation a, uint32_t x, uint32_t y) {
    return ((double4 *)rsGetElementAt(a, x, y))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline char __attribute__((overloadable))
    rsGetElementAt_char(rs_allocation a, uint32_t x, uint32_t y) {
    return ((char *)rsGetElementAt(a, x, y))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline char2 __attribute__((overloadable))
    rsGetElementAt_char2(rs_allocation a, uint32_t x, uint32_t y) {
    return ((char2 *)rsGetElementAt(a, x, y))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline char3 __attribute__((overloadable))
    rsGetElementAt_char3(rs_allocation a, uint32_t x, uint32_t y) {
    return ((char3 *)rsGetElementAt(a, x, y))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline char4 __attribute__((overloadable))
    rsGetElementAt_char4(rs_allocation a, uint32_t x, uint32_t y) {
    return ((char4 *)rsGetElementAt(a, x, y))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline uchar __attribute__((overloadable))
    rsGetElementAt_uchar(rs_allocation a, uint32_t x, uint32_t y) {
    return ((uchar *)rsGetElementAt(a, x, y))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline uchar2 __attribute__((overloadable))
    rsGetElementAt_uchar2(rs_allocation a, uint32_t x, uint32_t y) {
    return ((uchar2 *)rsGetElementAt(a, x, y))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline uchar3 __attribute__((overloadable))
    rsGetElementAt_uchar3(rs_allocation a, uint32_t x, uint32_t y) {
    return ((uchar3 *)rsGetElementAt(a, x, y))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline uchar4 __attribute__((overloadable))
    rsGetElementAt_uchar4(rs_allocation a, uint32_t x, uint32_t y) {
    return ((uchar4 *)rsGetElementAt(a, x, y))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline short __attribute__((overloadable))
    rsGetElementAt_short(rs_allocation a, uint32_t x, uint32_t y) {
    return ((short *)rsGetElementAt(a, x, y))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline short2 __attribute__((overloadable))
    rsGetElementAt_short2(rs_allocation a, uint32_t x, uint32_t y) {
    return ((short2 *)rsGetElementAt(a, x, y))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline short3 __attribute__((overloadable))
    rsGetElementAt_short3(rs_allocation a, uint32_t x, uint32_t y) {
    return ((short3 *)rsGetElementAt(a, x, y))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline short4 __attribute__((overloadable))
    rsGetElementAt_short4(rs_allocation a, uint32_t x, uint32_t y) {
    return ((short4 *)rsGetElementAt(a, x, y))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline ushort __attribute__((overloadable))
    rsGetElementAt_ushort(rs_allocation a, uint32_t x, uint32_t y) {
    return ((ushort *)rsGetElementAt(a, x, y))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline ushort2 __attribute__((overloadable))
    rsGetElementAt_ushort2(rs_allocation a, uint32_t x, uint32_t y) {
    return ((ushort2 *)rsGetElementAt(a, x, y))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline ushort3 __attribute__((overloadable))
    rsGetElementAt_ushort3(rs_allocation a, uint32_t x, uint32_t y) {
    return ((ushort3 *)rsGetElementAt(a, x, y))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline ushort4 __attribute__((overloadable))
    rsGetElementAt_ushort4(rs_allocation a, uint32_t x, uint32_t y) {
    return ((ushort4 *)rsGetElementAt(a, x, y))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline int __attribute__((overloadable))
    rsGetElementAt_int(rs_allocation a, uint32_t x, uint32_t y) {
    return ((int *)rsGetElementAt(a, x, y))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline int2 __attribute__((overloadable))
    rsGetElementAt_int2(rs_allocation a, uint32_t x, uint32_t y) {
    return ((int2 *)rsGetElementAt(a, x, y))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline int3 __attribute__((overloadable))
    rsGetElementAt_int3(rs_allocation a, uint32_t x, uint32_t y) {
    return ((int3 *)rsGetElementAt(a, x, y))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline int4 __attribute__((overloadable))
    rsGetElementAt_int4(rs_allocation a, uint32_t x, uint32_t y) {
    return ((int4 *)rsGetElementAt(a, x, y))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline uint __attribute__((overloadable))
    rsGetElementAt_uint(rs_allocation a, uint32_t x, uint32_t y) {
    return ((uint *)rsGetElementAt(a, x, y))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline uint2 __attribute__((overloadable))
    rsGetElementAt_uint2(rs_allocation a, uint32_t x, uint32_t y) {
    return ((uint2 *)rsGetElementAt(a, x, y))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline uint3 __attribute__((overloadable))
    rsGetElementAt_uint3(rs_allocation a, uint32_t x, uint32_t y) {
    return ((uint3 *)rsGetElementAt(a, x, y))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline uint4 __attribute__((overloadable))
    rsGetElementAt_uint4(rs_allocation a, uint32_t x, uint32_t y) {
    return ((uint4 *)rsGetElementAt(a, x, y))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline long __attribute__((overloadable))
    rsGetElementAt_long(rs_allocation a, uint32_t x, uint32_t y) {
    return ((long *)rsGetElementAt(a, x, y))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline long2 __attribute__((overloadable))
    rsGetElementAt_long2(rs_allocation a, uint32_t x, uint32_t y) {
    return ((long2 *)rsGetElementAt(a, x, y))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline long3 __attribute__((overloadable))
    rsGetElementAt_long3(rs_allocation a, uint32_t x, uint32_t y) {
    return ((long3 *)rsGetElementAt(a, x, y))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline long4 __attribute__((overloadable))
    rsGetElementAt_long4(rs_allocation a, uint32_t x, uint32_t y) {
    return ((long4 *)rsGetElementAt(a, x, y))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline ulong __attribute__((overloadable))
    rsGetElementAt_ulong(rs_allocation a, uint32_t x, uint32_t y) {
    return ((ulong *)rsGetElementAt(a, x, y))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline ulong2 __attribute__((overloadable))
    rsGetElementAt_ulong2(rs_allocation a, uint32_t x, uint32_t y) {
    return ((ulong2 *)rsGetElementAt(a, x, y))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline ulong3 __attribute__((overloadable))
    rsGetElementAt_ulong3(rs_allocation a, uint32_t x, uint32_t y) {
    return ((ulong3 *)rsGetElementAt(a, x, y))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline ulong4 __attribute__((overloadable))
    rsGetElementAt_ulong4(rs_allocation a, uint32_t x, uint32_t y) {
    return ((ulong4 *)rsGetElementAt(a, x, y))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline float __attribute__((overloadable))
    rsGetElementAt_float(rs_allocation a, uint32_t x, uint32_t y, uint32_t z) {
    return ((float *)rsGetElementAt(a, x, y, z))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline float2 __attribute__((overloadable))
    rsGetElementAt_float2(rs_allocation a, uint32_t x, uint32_t y, uint32_t z) {
    return ((float2 *)rsGetElementAt(a, x, y, z))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline float3 __attribute__((overloadable))
    rsGetElementAt_float3(rs_allocation a, uint32_t x, uint32_t y, uint32_t z) {
    return ((float3 *)rsGetElementAt(a, x, y, z))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline float4 __attribute__((overloadable))
    rsGetElementAt_float4(rs_allocation a, uint32_t x, uint32_t y, uint32_t z) {
    return ((float4 *)rsGetElementAt(a, x, y, z))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline double __attribute__((overloadable))
    rsGetElementAt_double(rs_allocation a, uint32_t x, uint32_t y, uint32_t z) {
    return ((double *)rsGetElementAt(a, x, y, z))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline double2 __attribute__((overloadable))
    rsGetElementAt_double2(rs_allocation a, uint32_t x, uint32_t y, uint32_t z) {
    return ((double2 *)rsGetElementAt(a, x, y, z))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline double3 __attribute__((overloadable))
    rsGetElementAt_double3(rs_allocation a, uint32_t x, uint32_t y, uint32_t z) {
    return ((double3 *)rsGetElementAt(a, x, y, z))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline double4 __attribute__((overloadable))
    rsGetElementAt_double4(rs_allocation a, uint32_t x, uint32_t y, uint32_t z) {
    return ((double4 *)rsGetElementAt(a, x, y, z))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline char __attribute__((overloadable))
    rsGetElementAt_char(rs_allocation a, uint32_t x, uint32_t y, uint32_t z) {
    return ((char *)rsGetElementAt(a, x, y, z))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline char2 __attribute__((overloadable))
    rsGetElementAt_char2(rs_allocation a, uint32_t x, uint32_t y, uint32_t z) {
    return ((char2 *)rsGetElementAt(a, x, y, z))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline char3 __attribute__((overloadable))
    rsGetElementAt_char3(rs_allocation a, uint32_t x, uint32_t y, uint32_t z) {
    return ((char3 *)rsGetElementAt(a, x, y, z))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline char4 __attribute__((overloadable))
    rsGetElementAt_char4(rs_allocation a, uint32_t x, uint32_t y, uint32_t z) {
    return ((char4 *)rsGetElementAt(a, x, y, z))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline uchar __attribute__((overloadable))
    rsGetElementAt_uchar(rs_allocation a, uint32_t x, uint32_t y, uint32_t z) {
    return ((uchar *)rsGetElementAt(a, x, y, z))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline uchar2 __attribute__((overloadable))
    rsGetElementAt_uchar2(rs_allocation a, uint32_t x, uint32_t y, uint32_t z) {
    return ((uchar2 *)rsGetElementAt(a, x, y, z))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline uchar3 __attribute__((overloadable))
    rsGetElementAt_uchar3(rs_allocation a, uint32_t x, uint32_t y, uint32_t z) {
    return ((uchar3 *)rsGetElementAt(a, x, y, z))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline uchar4 __attribute__((overloadable))
    rsGetElementAt_uchar4(rs_allocation a, uint32_t x, uint32_t y, uint32_t z) {
    return ((uchar4 *)rsGetElementAt(a, x, y, z))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline short __attribute__((overloadable))
    rsGetElementAt_short(rs_allocation a, uint32_t x, uint32_t y, uint32_t z) {
    return ((short *)rsGetElementAt(a, x, y, z))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline short2 __attribute__((overloadable))
    rsGetElementAt_short2(rs_allocation a, uint32_t x, uint32_t y, uint32_t z) {
    return ((short2 *)rsGetElementAt(a, x, y, z))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline short3 __attribute__((overloadable))
    rsGetElementAt_short3(rs_allocation a, uint32_t x, uint32_t y, uint32_t z) {
    return ((short3 *)rsGetElementAt(a, x, y, z))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline short4 __attribute__((overloadable))
    rsGetElementAt_short4(rs_allocation a, uint32_t x, uint32_t y, uint32_t z) {
    return ((short4 *)rsGetElementAt(a, x, y, z))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline ushort __attribute__((overloadable))
    rsGetElementAt_ushort(rs_allocation a, uint32_t x, uint32_t y, uint32_t z) {
    return ((ushort *)rsGetElementAt(a, x, y, z))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline ushort2 __attribute__((overloadable))
    rsGetElementAt_ushort2(rs_allocation a, uint32_t x, uint32_t y, uint32_t z) {
    return ((ushort2 *)rsGetElementAt(a, x, y, z))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline ushort3 __attribute__((overloadable))
    rsGetElementAt_ushort3(rs_allocation a, uint32_t x, uint32_t y, uint32_t z) {
    return ((ushort3 *)rsGetElementAt(a, x, y, z))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline ushort4 __attribute__((overloadable))
    rsGetElementAt_ushort4(rs_allocation a, uint32_t x, uint32_t y, uint32_t z) {
    return ((ushort4 *)rsGetElementAt(a, x, y, z))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline int __attribute__((overloadable))
    rsGetElementAt_int(rs_allocation a, uint32_t x, uint32_t y, uint32_t z) {
    return ((int *)rsGetElementAt(a, x, y, z))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline int2 __attribute__((overloadable))
    rsGetElementAt_int2(rs_allocation a, uint32_t x, uint32_t y, uint32_t z) {
    return ((int2 *)rsGetElementAt(a, x, y, z))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline int3 __attribute__((overloadable))
    rsGetElementAt_int3(rs_allocation a, uint32_t x, uint32_t y, uint32_t z) {
    return ((int3 *)rsGetElementAt(a, x, y, z))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline int4 __attribute__((overloadable))
    rsGetElementAt_int4(rs_allocation a, uint32_t x, uint32_t y, uint32_t z) {
    return ((int4 *)rsGetElementAt(a, x, y, z))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline uint __attribute__((overloadable))
    rsGetElementAt_uint(rs_allocation a, uint32_t x, uint32_t y, uint32_t z) {
    return ((uint *)rsGetElementAt(a, x, y, z))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline uint2 __attribute__((overloadable))
    rsGetElementAt_uint2(rs_allocation a, uint32_t x, uint32_t y, uint32_t z) {
    return ((uint2 *)rsGetElementAt(a, x, y, z))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline uint3 __attribute__((overloadable))
    rsGetElementAt_uint3(rs_allocation a, uint32_t x, uint32_t y, uint32_t z) {
    return ((uint3 *)rsGetElementAt(a, x, y, z))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline uint4 __attribute__((overloadable))
    rsGetElementAt_uint4(rs_allocation a, uint32_t x, uint32_t y, uint32_t z) {
    return ((uint4 *)rsGetElementAt(a, x, y, z))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline long __attribute__((overloadable))
    rsGetElementAt_long(rs_allocation a, uint32_t x, uint32_t y, uint32_t z) {
    return ((long *)rsGetElementAt(a, x, y, z))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline long2 __attribute__((overloadable))
    rsGetElementAt_long2(rs_allocation a, uint32_t x, uint32_t y, uint32_t z) {
    return ((long2 *)rsGetElementAt(a, x, y, z))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline long3 __attribute__((overloadable))
    rsGetElementAt_long3(rs_allocation a, uint32_t x, uint32_t y, uint32_t z) {
    return ((long3 *)rsGetElementAt(a, x, y, z))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline long4 __attribute__((overloadable))
    rsGetElementAt_long4(rs_allocation a, uint32_t x, uint32_t y, uint32_t z) {
    return ((long4 *)rsGetElementAt(a, x, y, z))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline ulong __attribute__((overloadable))
    rsGetElementAt_ulong(rs_allocation a, uint32_t x, uint32_t y, uint32_t z) {
    return ((ulong *)rsGetElementAt(a, x, y, z))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline ulong2 __attribute__((overloadable))
    rsGetElementAt_ulong2(rs_allocation a, uint32_t x, uint32_t y, uint32_t z) {
    return ((ulong2 *)rsGetElementAt(a, x, y, z))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline ulong3 __attribute__((overloadable))
    rsGetElementAt_ulong3(rs_allocation a, uint32_t x, uint32_t y, uint32_t z) {
    return ((ulong3 *)rsGetElementAt(a, x, y, z))[0];
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 17)
static inline ulong4 __attribute__((overloadable))
    rsGetElementAt_ulong4(rs_allocation a, uint32_t x, uint32_t y, uint32_t z) {
    return ((ulong4 *)rsGetElementAt(a, x, y, z))[0];
}
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern float __attribute__((overloadable))
    rsGetElementAt_float(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern float2 __attribute__((overloadable))
    rsGetElementAt_float2(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern float3 __attribute__((overloadable))
    rsGetElementAt_float3(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern float4 __attribute__((overloadable))
    rsGetElementAt_float4(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern double __attribute__((overloadable))
    rsGetElementAt_double(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern double2 __attribute__((overloadable))
    rsGetElementAt_double2(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern double3 __attribute__((overloadable))
    rsGetElementAt_double3(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern double4 __attribute__((overloadable))
    rsGetElementAt_double4(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern char __attribute__((overloadable))
    rsGetElementAt_char(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern char2 __attribute__((overloadable))
    rsGetElementAt_char2(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern char3 __attribute__((overloadable))
    rsGetElementAt_char3(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern char4 __attribute__((overloadable))
    rsGetElementAt_char4(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern uchar __attribute__((overloadable))
    rsGetElementAt_uchar(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern uchar2 __attribute__((overloadable))
    rsGetElementAt_uchar2(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern uchar3 __attribute__((overloadable))
    rsGetElementAt_uchar3(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern uchar4 __attribute__((overloadable))
    rsGetElementAt_uchar4(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern short __attribute__((overloadable))
    rsGetElementAt_short(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern short2 __attribute__((overloadable))
    rsGetElementAt_short2(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern short3 __attribute__((overloadable))
    rsGetElementAt_short3(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern short4 __attribute__((overloadable))
    rsGetElementAt_short4(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern ushort __attribute__((overloadable))
    rsGetElementAt_ushort(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern ushort2 __attribute__((overloadable))
    rsGetElementAt_ushort2(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern ushort3 __attribute__((overloadable))
    rsGetElementAt_ushort3(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern ushort4 __attribute__((overloadable))
    rsGetElementAt_ushort4(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern int __attribute__((overloadable))
    rsGetElementAt_int(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern int2 __attribute__((overloadable))
    rsGetElementAt_int2(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern int3 __attribute__((overloadable))
    rsGetElementAt_int3(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern int4 __attribute__((overloadable))
    rsGetElementAt_int4(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern uint __attribute__((overloadable))
    rsGetElementAt_uint(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern uint2 __attribute__((overloadable))
    rsGetElementAt_uint2(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern uint3 __attribute__((overloadable))
    rsGetElementAt_uint3(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern uint4 __attribute__((overloadable))
    rsGetElementAt_uint4(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern long __attribute__((overloadable))
    rsGetElementAt_long(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern long2 __attribute__((overloadable))
    rsGetElementAt_long2(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern long3 __attribute__((overloadable))
    rsGetElementAt_long3(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern long4 __attribute__((overloadable))
    rsGetElementAt_long4(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern ulong __attribute__((overloadable))
    rsGetElementAt_ulong(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern ulong2 __attribute__((overloadable))
    rsGetElementAt_ulong2(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern ulong3 __attribute__((overloadable))
    rsGetElementAt_ulong3(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern ulong4 __attribute__((overloadable))
    rsGetElementAt_ulong4(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern float __attribute__((overloadable))
    rsGetElementAt_float(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern float2 __attribute__((overloadable))
    rsGetElementAt_float2(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern float3 __attribute__((overloadable))
    rsGetElementAt_float3(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern float4 __attribute__((overloadable))
    rsGetElementAt_float4(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern double __attribute__((overloadable))
    rsGetElementAt_double(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern double2 __attribute__((overloadable))
    rsGetElementAt_double2(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern double3 __attribute__((overloadable))
    rsGetElementAt_double3(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern double4 __attribute__((overloadable))
    rsGetElementAt_double4(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern char __attribute__((overloadable))
    rsGetElementAt_char(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern char2 __attribute__((overloadable))
    rsGetElementAt_char2(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern char3 __attribute__((overloadable))
    rsGetElementAt_char3(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern char4 __attribute__((overloadable))
    rsGetElementAt_char4(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern uchar __attribute__((overloadable))
    rsGetElementAt_uchar(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern uchar2 __attribute__((overloadable))
    rsGetElementAt_uchar2(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern uchar3 __attribute__((overloadable))
    rsGetElementAt_uchar3(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern uchar4 __attribute__((overloadable))
    rsGetElementAt_uchar4(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern short __attribute__((overloadable))
    rsGetElementAt_short(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern short2 __attribute__((overloadable))
    rsGetElementAt_short2(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern short3 __attribute__((overloadable))
    rsGetElementAt_short3(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern short4 __attribute__((overloadable))
    rsGetElementAt_short4(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern ushort __attribute__((overloadable))
    rsGetElementAt_ushort(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern ushort2 __attribute__((overloadable))
    rsGetElementAt_ushort2(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern ushort3 __attribute__((overloadable))
    rsGetElementAt_ushort3(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern ushort4 __attribute__((overloadable))
    rsGetElementAt_ushort4(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern int __attribute__((overloadable))
    rsGetElementAt_int(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern int2 __attribute__((overloadable))
    rsGetElementAt_int2(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern int3 __attribute__((overloadable))
    rsGetElementAt_int3(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern int4 __attribute__((overloadable))
    rsGetElementAt_int4(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern uint __attribute__((overloadable))
    rsGetElementAt_uint(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern uint2 __attribute__((overloadable))
    rsGetElementAt_uint2(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern uint3 __attribute__((overloadable))
    rsGetElementAt_uint3(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern uint4 __attribute__((overloadable))
    rsGetElementAt_uint4(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern long __attribute__((overloadable))
    rsGetElementAt_long(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern long2 __attribute__((overloadable))
    rsGetElementAt_long2(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern long3 __attribute__((overloadable))
    rsGetElementAt_long3(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern long4 __attribute__((overloadable))
    rsGetElementAt_long4(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern ulong __attribute__((overloadable))
    rsGetElementAt_ulong(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern ulong2 __attribute__((overloadable))
    rsGetElementAt_ulong2(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern ulong3 __attribute__((overloadable))
    rsGetElementAt_ulong3(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern ulong4 __attribute__((overloadable))
    rsGetElementAt_ulong4(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern float __attribute__((overloadable))
    rsGetElementAt_float(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern float2 __attribute__((overloadable))
    rsGetElementAt_float2(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern float3 __attribute__((overloadable))
    rsGetElementAt_float3(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern float4 __attribute__((overloadable))
    rsGetElementAt_float4(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern double __attribute__((overloadable))
    rsGetElementAt_double(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern double2 __attribute__((overloadable))
    rsGetElementAt_double2(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern double3 __attribute__((overloadable))
    rsGetElementAt_double3(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern double4 __attribute__((overloadable))
    rsGetElementAt_double4(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern char __attribute__((overloadable))
    rsGetElementAt_char(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern char2 __attribute__((overloadable))
    rsGetElementAt_char2(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern char3 __attribute__((overloadable))
    rsGetElementAt_char3(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern char4 __attribute__((overloadable))
    rsGetElementAt_char4(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern uchar __attribute__((overloadable))
    rsGetElementAt_uchar(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern uchar2 __attribute__((overloadable))
    rsGetElementAt_uchar2(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern uchar3 __attribute__((overloadable))
    rsGetElementAt_uchar3(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern uchar4 __attribute__((overloadable))
    rsGetElementAt_uchar4(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern short __attribute__((overloadable))
    rsGetElementAt_short(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern short2 __attribute__((overloadable))
    rsGetElementAt_short2(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern short3 __attribute__((overloadable))
    rsGetElementAt_short3(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern short4 __attribute__((overloadable))
    rsGetElementAt_short4(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern ushort __attribute__((overloadable))
    rsGetElementAt_ushort(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern ushort2 __attribute__((overloadable))
    rsGetElementAt_ushort2(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern ushort3 __attribute__((overloadable))
    rsGetElementAt_ushort3(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern ushort4 __attribute__((overloadable))
    rsGetElementAt_ushort4(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern int __attribute__((overloadable))
    rsGetElementAt_int(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern int2 __attribute__((overloadable))
    rsGetElementAt_int2(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern int3 __attribute__((overloadable))
    rsGetElementAt_int3(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern int4 __attribute__((overloadable))
    rsGetElementAt_int4(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern uint __attribute__((overloadable))
    rsGetElementAt_uint(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern uint2 __attribute__((overloadable))
    rsGetElementAt_uint2(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern uint3 __attribute__((overloadable))
    rsGetElementAt_uint3(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern uint4 __attribute__((overloadable))
    rsGetElementAt_uint4(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern long __attribute__((overloadable))
    rsGetElementAt_long(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern long2 __attribute__((overloadable))
    rsGetElementAt_long2(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern long3 __attribute__((overloadable))
    rsGetElementAt_long3(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern long4 __attribute__((overloadable))
    rsGetElementAt_long4(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern ulong __attribute__((overloadable))
    rsGetElementAt_ulong(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern ulong2 __attribute__((overloadable))
    rsGetElementAt_ulong2(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern ulong3 __attribute__((overloadable))
    rsGetElementAt_ulong3(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern ulong4 __attribute__((overloadable))
    rsGetElementAt_ulong4(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 23))
extern half __attribute__((overloadable))
    rsGetElementAt_half(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 23))
extern half2 __attribute__((overloadable))
    rsGetElementAt_half2(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 23))
extern half3 __attribute__((overloadable))
    rsGetElementAt_half3(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 23))
extern half4 __attribute__((overloadable))
    rsGetElementAt_half4(rs_allocation a, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 23))
extern half __attribute__((overloadable))
    rsGetElementAt_half(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 23))
extern half2 __attribute__((overloadable))
    rsGetElementAt_half2(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 23))
extern half3 __attribute__((overloadable))
    rsGetElementAt_half3(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 23))
extern half4 __attribute__((overloadable))
    rsGetElementAt_half4(rs_allocation a, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 23))
extern half __attribute__((overloadable))
    rsGetElementAt_half(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 23))
extern half2 __attribute__((overloadable))
    rsGetElementAt_half2(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 23))
extern half3 __attribute__((overloadable))
    rsGetElementAt_half3(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 23))
extern half4 __attribute__((overloadable))
    rsGetElementAt_half4(rs_allocation a, uint32_t x, uint32_t y, uint32_t z);
#endif

/*
 * rsGetElementAtYuv_uchar_U: Get the U component of an allocation of YUVs
 *
 * Extracts the U component of a single YUV value from a 2D allocation of YUVs.
 *
 * Inside an allocation, Y, U, and V components may be stored if different planes
 * and at different resolutions.  The x, y coordinates provided here are in the
 * dimensions of the Y plane.
 *
 * See rsGetElementAtYuv_uchar_Y().
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern uchar __attribute__((overloadable))
    rsGetElementAtYuv_uchar_U(rs_allocation a, uint32_t x, uint32_t y);
#endif

/*
 * rsGetElementAtYuv_uchar_V: Get the V component of an allocation of YUVs
 *
 * Extracts the V component of a single YUV value from a 2D allocation of YUVs.
 *
 * Inside an allocation, Y, U, and V components may be stored if different planes
 * and at different resolutions.  The x, y coordinates provided here are in the
 * dimensions of the Y plane.
 *
 * See rsGetElementAtYuv_uchar_Y().
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern uchar __attribute__((overloadable))
    rsGetElementAtYuv_uchar_V(rs_allocation a, uint32_t x, uint32_t y);
#endif

/*
 * rsGetElementAtYuv_uchar_Y: Get the Y component of an allocation of YUVs
 *
 * Extracts the Y component of a single YUV value from a 2D allocation of YUVs.
 *
 * Inside an allocation, Y, U, and V components may be stored if different planes
 * and at different resolutions.  The x, y coordinates provided here are in the
 * dimensions of the Y plane.
 *
 * See rsGetElementAtYuv_uchar_U() and rsGetElementAtYuv_uchar_V().
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern uchar __attribute__((overloadable))
    rsGetElementAtYuv_uchar_Y(rs_allocation a, uint32_t x, uint32_t y);
#endif

/*
 * rsSample: Sample a value from a texture allocation
 *
 * Fetches a value from a texture allocation in a way described by the sampler.
 *
 * If your allocation is 1D, use the variant with float for location.  For 2D,
 * use the float2 variant.
 *
 * See android.renderscript.Sampler for more details.
 *
 * Parameters:
 *   a: Allocation to sample from.
 *   s: Sampler state.
 *   location: Location to sample from.
 *   lod: Mip level to sample from, for fractional values mip levels will be interpolated if RS_SAMPLER_LINEAR_MIP_LINEAR is used.
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 16))
extern float4 __attribute__((overloadable))
    rsSample(rs_allocation a, rs_sampler s, float location);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 16))
extern float4 __attribute__((overloadable))
    rsSample(rs_allocation a, rs_sampler s, float location, float lod);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 16))
extern float4 __attribute__((overloadable))
    rsSample(rs_allocation a, rs_sampler s, float2 location);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 16))
extern float4 __attribute__((overloadable))
    rsSample(rs_allocation a, rs_sampler s, float2 location, float lod);
#endif

/*
 * rsSetElementAt: Set a cell of an allocation
 *
 * This function stores a value into a single cell of an allocation.
 *
 * When storing into a three dimensional allocations, use the x, y, z variant.
 * Similarly, use the x, y variant for two dimensional allocations and x for
 * the mono dimensional allocations.
 *
 * This function has two styles.  One passes the value to be stored using a void*,
 * the other has the actual value as an argument, e.g. rsSetElementAt() vs.
 * rsSetElementAt_int4().  For primitive types, always use the latter as it is
 * more efficient.
 *
 * See also rsGetElementAt().
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt(rs_allocation a, void* ptr, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt(rs_allocation a, void* ptr, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_float(rs_allocation a, float val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_float2(rs_allocation a, float2 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_float3(rs_allocation a, float3 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_float4(rs_allocation a, float4 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_double(rs_allocation a, double val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_double2(rs_allocation a, double2 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_double3(rs_allocation a, double3 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_double4(rs_allocation a, double4 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_char(rs_allocation a, char val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_char2(rs_allocation a, char2 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_char3(rs_allocation a, char3 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_char4(rs_allocation a, char4 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_uchar(rs_allocation a, uchar val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_uchar2(rs_allocation a, uchar2 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_uchar3(rs_allocation a, uchar3 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_uchar4(rs_allocation a, uchar4 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_short(rs_allocation a, short val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_short2(rs_allocation a, short2 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_short3(rs_allocation a, short3 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_short4(rs_allocation a, short4 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_ushort(rs_allocation a, ushort val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_ushort2(rs_allocation a, ushort2 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_ushort3(rs_allocation a, ushort3 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_ushort4(rs_allocation a, ushort4 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_int(rs_allocation a, int val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_int2(rs_allocation a, int2 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_int3(rs_allocation a, int3 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_int4(rs_allocation a, int4 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_uint(rs_allocation a, uint val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_uint2(rs_allocation a, uint2 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_uint3(rs_allocation a, uint3 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_uint4(rs_allocation a, uint4 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_long(rs_allocation a, long val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_long2(rs_allocation a, long2 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_long3(rs_allocation a, long3 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_long4(rs_allocation a, long4 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_ulong(rs_allocation a, ulong val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_ulong2(rs_allocation a, ulong2 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_ulong3(rs_allocation a, ulong3 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_ulong4(rs_allocation a, ulong4 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_float(rs_allocation a, float val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_float2(rs_allocation a, float2 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_float3(rs_allocation a, float3 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_float4(rs_allocation a, float4 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_double(rs_allocation a, double val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_double2(rs_allocation a, double2 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_double3(rs_allocation a, double3 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_double4(rs_allocation a, double4 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_char(rs_allocation a, char val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_char2(rs_allocation a, char2 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_char3(rs_allocation a, char3 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_char4(rs_allocation a, char4 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_uchar(rs_allocation a, uchar val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_uchar2(rs_allocation a, uchar2 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_uchar3(rs_allocation a, uchar3 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_uchar4(rs_allocation a, uchar4 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_short(rs_allocation a, short val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_short2(rs_allocation a, short2 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_short3(rs_allocation a, short3 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_short4(rs_allocation a, short4 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_ushort(rs_allocation a, ushort val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_ushort2(rs_allocation a, ushort2 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_ushort3(rs_allocation a, ushort3 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_ushort4(rs_allocation a, ushort4 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_int(rs_allocation a, int val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_int2(rs_allocation a, int2 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_int3(rs_allocation a, int3 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_int4(rs_allocation a, int4 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_uint(rs_allocation a, uint val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_uint2(rs_allocation a, uint2 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_uint3(rs_allocation a, uint3 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_uint4(rs_allocation a, uint4 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_long(rs_allocation a, long val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_long2(rs_allocation a, long2 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_long3(rs_allocation a, long3 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_long4(rs_allocation a, long4 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_ulong(rs_allocation a, ulong val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_ulong2(rs_allocation a, ulong2 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_ulong3(rs_allocation a, ulong3 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_ulong4(rs_allocation a, ulong4 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_float(rs_allocation a, float val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_float2(rs_allocation a, float2 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_float3(rs_allocation a, float3 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_float4(rs_allocation a, float4 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_double(rs_allocation a, double val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_double2(rs_allocation a, double2 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_double3(rs_allocation a, double3 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_double4(rs_allocation a, double4 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_char(rs_allocation a, char val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_char2(rs_allocation a, char2 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_char3(rs_allocation a, char3 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_char4(rs_allocation a, char4 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_uchar(rs_allocation a, uchar val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_uchar2(rs_allocation a, uchar2 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_uchar3(rs_allocation a, uchar3 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_uchar4(rs_allocation a, uchar4 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_short(rs_allocation a, short val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_short2(rs_allocation a, short2 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_short3(rs_allocation a, short3 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_short4(rs_allocation a, short4 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_ushort(rs_allocation a, ushort val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_ushort2(rs_allocation a, ushort2 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_ushort3(rs_allocation a, ushort3 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_ushort4(rs_allocation a, ushort4 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_int(rs_allocation a, int val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_int2(rs_allocation a, int2 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_int3(rs_allocation a, int3 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_int4(rs_allocation a, int4 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_uint(rs_allocation a, uint val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_uint2(rs_allocation a, uint2 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_uint3(rs_allocation a, uint3 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_uint4(rs_allocation a, uint4 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_long(rs_allocation a, long val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_long2(rs_allocation a, long2 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_long3(rs_allocation a, long3 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_long4(rs_allocation a, long4 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_ulong(rs_allocation a, ulong val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_ulong2(rs_allocation a, ulong2 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_ulong3(rs_allocation a, ulong3 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern void __attribute__((overloadable))
    rsSetElementAt_ulong4(rs_allocation a, ulong4 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 23))
extern void __attribute__((overloadable))
    rsSetElementAt_half(rs_allocation a, half val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 23))
extern void __attribute__((overloadable))
    rsSetElementAt_half2(rs_allocation a, half2 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 23))
extern void __attribute__((overloadable))
    rsSetElementAt_half3(rs_allocation a, half3 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 23))
extern void __attribute__((overloadable))
    rsSetElementAt_half4(rs_allocation a, half4 val, uint32_t x);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 23))
extern void __attribute__((overloadable))
    rsSetElementAt_half(rs_allocation a, half val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 23))
extern void __attribute__((overloadable))
    rsSetElementAt_half2(rs_allocation a, half2 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 23))
extern void __attribute__((overloadable))
    rsSetElementAt_half3(rs_allocation a, half3 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 23))
extern void __attribute__((overloadable))
    rsSetElementAt_half4(rs_allocation a, half4 val, uint32_t x, uint32_t y);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 23))
extern void __attribute__((overloadable))
    rsSetElementAt_half(rs_allocation a, half val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 23))
extern void __attribute__((overloadable))
    rsSetElementAt_half2(rs_allocation a, half2 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 23))
extern void __attribute__((overloadable))
    rsSetElementAt_half3(rs_allocation a, half3 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 23))
extern void __attribute__((overloadable))
    rsSetElementAt_half4(rs_allocation a, half4 val, uint32_t x, uint32_t y, uint32_t z);
#endif

#endif // RENDERSCRIPT_RS_ALLOCATION_DATA_RSH
