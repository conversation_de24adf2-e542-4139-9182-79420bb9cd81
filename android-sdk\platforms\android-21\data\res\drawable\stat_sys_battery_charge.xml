<?xml version="1.0" encoding="utf-8"?>
<!--
** Copyright 2007, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License"); 
** you may not use this file except in compliance with the License. 
** You may obtain a copy of the License at 
**
**     http://www.apache.org/licenses/LICENSE-2.0 
**
** Unless required by applicable law or agreed to in writing, software 
** distributed under the License is distributed on an "AS IS" BASIS, 
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. 
** See the License for the specific language governing permissions and 
** limitations under the License.
*/
-->

<level-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:maxLevel="4" android:drawable="@android:drawable/stat_sys_battery_charge_anim0" />
    <item android:maxLevel="15" android:drawable="@android:drawable/stat_sys_battery_charge_anim15" />
    <item android:maxLevel="35" android:drawable="@android:drawable/stat_sys_battery_charge_anim28" />
    <item android:maxLevel="49" android:drawable="@android:drawable/stat_sys_battery_charge_anim43" />
    <item android:maxLevel="60" android:drawable="@android:drawable/stat_sys_battery_charge_anim57" />
    <item android:maxLevel="75" android:drawable="@android:drawable/stat_sys_battery_charge_anim71" />
    <item android:maxLevel="90" android:drawable="@android:drawable/stat_sys_battery_charge_anim85" />
    <item android:maxLevel="100" android:drawable="@android:drawable/stat_sys_battery_charge_anim100" />
</level-list>


