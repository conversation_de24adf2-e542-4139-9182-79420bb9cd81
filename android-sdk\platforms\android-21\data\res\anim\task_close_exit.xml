<?xml version="1.0" encoding="utf-8"?>
<!--
/*
** Copyright 2009, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<set xmlns:android="http://schemas.android.com/apk/res/android"
        android:background="#ff000000" android:shareInterpolator="false" android:zAdjustment="top">

    <alpha android:fromAlpha="1.0" android:toAlpha="0"
            android:fillEnabled="true" android:fillBefore="true" android:fillAfter="true"
            android:interpolator="@interpolator/accelerate_quad"
            android:startOffset="250"
            android:duration="167"/>

    <translate android:fromYDelta="0" android:toYDelta="110%"
            android:fillEnabled="true" android:fillBefore="true" android:fillAfter="true"
            android:interpolator="@interpolator/accelerate_quint"
            android:duration="417"/>

    <!-- This is needed to keep the animation running while task_open_enter completes -->
    <alpha android:fromAlpha="1.0" android:toAlpha="1.0"
            android:duration="700" />
</set>