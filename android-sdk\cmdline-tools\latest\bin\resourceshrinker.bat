@if "%DEBUG%" == "" @echo off
@rem ##########################################################################
@rem
@rem  resourceshrinker startup script for Windows
@rem
@rem ##########################################################################

@rem Set local scope for the variables with windows NT shell
if "%OS%"=="Windows_NT" setlocal

set DIRNAME=%~dp0
if "%DIRNAME%" == "" set DIRNAME=.
set APP_BASE_NAME=%~n0
set APP_HOME=%DIRNAME%..

@rem Add default JVM options here. You can also use JAVA_OPTS and RESOURCESHRINKER_OPTS to pass JVM options to this script.
set DEFAULT_JVM_OPTS=

@rem Find java.exe
if defined JAVA_HOME goto findJavaFromJavaHome

set JAVA_EXE=java.exe
%JAVA_EXE% -version >NUL 2>&1
if "%ERRORLEVEL%" == "0" goto init

echo.
echo ERROR: JAVA_HOME is not set and no 'java' command could be found in your PATH.
echo.
echo Please set the JAVA_HOME variable in your environment to match the
echo location of your Java installation.

goto fail

:findJavaFromJavaHome
set JAVA_HOME=%JAVA_HOME:"=%
set JAVA_EXE=%JAVA_HOME%/bin/java.exe

if exist "%JAVA_EXE%" goto init

echo.
echo ERROR: JAVA_HOME is set to an invalid directory: %JAVA_HOME%
echo.
echo Please set the JAVA_HOME variable in your environment to match the
echo location of your Java installation.

goto fail

:init
@rem Get command-line arguments, handling Windows variants

if not "%OS%" == "Windows_NT" goto win9xME_args

:win9xME_args
@rem Slurp the command line arguments.
set CMD_LINE_ARGS=
set _SKIP=2

:win9xME_args_slurp
if "x%~1" == "x" goto execute

set CMD_LINE_ARGS=%*

:execute

@rem check whether we have the right version of java
setlocal enabledelayedexpansion
for /f "tokens=3" %%a in ( '""%JAVA_EXE%" -version 2>&1 |findstr "version""' ) do (
  for /f "tokens=1,2 delims=." %%b in ( 'echo %%a' ) do (
    set versionWithQ=%%b%%c
  )
)
set version=!versionWithQ:"=!

if !version! lss 170 if "%SKIP_JDK_VERSION_CHECK%" == "" (
  echo Java version 17 or higher is required.
  echo To override this check set SKIP_JDK_VERSION_CHECK
  goto :eof
)

@rem Setup the command line

set CLASSPATH=%APP_HOME%\lib\resourceshrinker-classpath.jar

@rem Execute resourceshrinker
"%JAVA_EXE%" %DEFAULT_JVM_OPTS% %JAVA_OPTS% %RESOURCESHRINKER_OPTS%  -classpath "%CLASSPATH%" com.android.build.shrinker.ResourceShrinkerCli %CMD_LINE_ARGS%

:end
@rem End local scope for the variables with windows NT shell
if "%ERRORLEVEL%"=="0" goto mainEnd

:fail
rem Set variable RESOURCESHRINKER_EXIT_CONSOLE if you need the _script_ return code instead of
rem the _cmd.exe /c_ return code!
if  not "" == "%RESOURCESHRINKER_EXIT_CONSOLE%" exit 1
exit /b 1

:mainEnd
if "%OS%"=="Windows_NT" endlocal

:omega
