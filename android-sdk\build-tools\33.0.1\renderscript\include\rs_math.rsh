/*
 * Copyright (C) 2016 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// Don't edit this file!  It is auto-generated by frameworks/rs/api/generate.sh.

/*
 * rs_math.rsh: Mathematical Constants and Functions
 *
 * The mathematical functions below can be applied to scalars and vectors.   When applied
 * to vectors, the returned value is a vector of the function applied to each entry of the input.
 *
 * For example:
 * float3 a, b;
 * // The following call sets
 * //   a.x to sin(b.x),
 * //   a.y to sin(b.y), and
 * //   a.z to sin(b.z).
 * a = sin(b);
 *
 *
 * See Vector Math Functions for functions like distance() and length() that interpret
 * instead the input as a single vector in n-dimensional space.
 *
 * The precision of the mathematical operations on 32 bit floats is affected by the pragmas
 * rs_fp_relaxed and rs_fp_full.  Under rs_fp_relaxed, subnormal values may be flushed to zero and
 * rounding may be done towards zero.  In comparison, rs_fp_full requires correct handling of
 * subnormal values, i.e. smaller than 1.17549435e-38f.  rs_fp_rull also requires round to nearest
 * with ties to even.
 *
 * Different precision/speed tradeoffs can be achieved by using variants of the common math
 * functions.  Functions with a name starting with
 * - native_: May have custom hardware implementations with weaker precision.  Additionally,
 *   subnormal values may be flushed to zero, rounding towards zero may be used, and NaN and
 *   infinity input may not be handled correctly.
 * - half_: May perform internal computations using 16 bit floats.  Additionally, subnormal
 *   values may be flushed to zero, and rounding towards zero may be used.
 *
 */

#ifndef RENDERSCRIPT_RS_MATH_RSH
#define RENDERSCRIPT_RS_MATH_RSH

/*
 * M_1_PI: 1 / pi, as a 32 bit float
 *
 * The inverse of pi, as a 32 bit float.
 */
static const float M_1_PI = 0.318309886183790671537767526745028724f;

/*
 * M_2_PI: 2 / pi, as a 32 bit float
 *
 * 2 divided by pi, as a 32 bit float.
 */
static const float M_2_PI = 0.636619772367581343075535053490057448f;

/*
 * M_2_PIl: 2 / pi, as a 32 bit float
 *
 * DEPRECATED.  Do not use.
 *
 * 2 divided by pi, as a 32 bit float.
 */
static const float M_2_PIl = 0.636619772367581343075535053490057448f;

/*
 * M_2_SQRTPI: 2 / sqrt(pi), as a 32 bit float
 *
 * 2 divided by the square root of pi, as a 32 bit float.
 */
static const float M_2_SQRTPI = 1.128379167095512573896158903121545172f;

/*
 * M_E: e, as a 32 bit float
 *
 * The number e, the base of the natural logarithm, as a 32 bit float.
 */
static const float M_E = 2.718281828459045235360287471352662498f;

/*
 * M_LN10: log_e(10), as a 32 bit float
 *
 * The natural logarithm of 10, as a 32 bit float.
 */
static const float M_LN10 = 2.302585092994045684017991454684364208f;

/*
 * M_LN2: log_e(2), as a 32 bit float
 *
 * The natural logarithm of 2, as a 32 bit float.
 */
static const float M_LN2 = 0.693147180559945309417232121458176568f;

/*
 * M_LOG10E: log_10(e), as a 32 bit float
 *
 * The logarithm base 10 of e, as a 32 bit float.
 */
static const float M_LOG10E = 0.434294481903251827651128918916605082f;

/*
 * M_LOG2E: log_2(e), as a 32 bit float
 *
 * The logarithm base 2 of e, as a 32 bit float.
 */
static const float M_LOG2E = 1.442695040888963407359924681001892137f;

/*
 * M_PI: pi, as a 32 bit float
 *
 * The constant pi, as a 32 bit float.
 */
static const float M_PI = 3.141592653589793238462643383279502884f;

/*
 * M_PI_2: pi / 2, as a 32 bit float
 *
 * Pi divided by 2, as a 32 bit float.
 */
static const float M_PI_2 = 1.570796326794896619231321691639751442f;

/*
 * M_PI_4: pi / 4, as a 32 bit float
 *
 * Pi divided by 4, as a 32 bit float.
 */
static const float M_PI_4 = 0.785398163397448309615660845819875721f;

/*
 * M_SQRT1_2: 1 / sqrt(2), as a 32 bit float
 *
 * The inverse of the square root of 2, as a 32 bit float.
 */
static const float M_SQRT1_2 = 0.707106781186547524400844362104849039f;

/*
 * M_SQRT2: sqrt(2), as a 32 bit float
 *
 * The square root of 2, as a 32 bit float.
 */
static const float M_SQRT2 = 1.414213562373095048801688724209698079f;

/*
 * abs: Absolute value of an integer
 *
 * Returns the absolute value of an integer.
 *
 * For floats, use fabs().
 */
extern uchar __attribute__((const, overloadable))
    abs(char v);

extern uchar2 __attribute__((const, overloadable))
    abs(char2 v);

extern uchar3 __attribute__((const, overloadable))
    abs(char3 v);

extern uchar4 __attribute__((const, overloadable))
    abs(char4 v);

extern ushort __attribute__((const, overloadable))
    abs(short v);

extern ushort2 __attribute__((const, overloadable))
    abs(short2 v);

extern ushort3 __attribute__((const, overloadable))
    abs(short3 v);

extern ushort4 __attribute__((const, overloadable))
    abs(short4 v);

extern uint __attribute__((const, overloadable))
    abs(int v);

extern uint2 __attribute__((const, overloadable))
    abs(int2 v);

extern uint3 __attribute__((const, overloadable))
    abs(int3 v);

extern uint4 __attribute__((const, overloadable))
    abs(int4 v);

/*
 * acos: Inverse cosine
 *
 * Returns the inverse cosine, in radians.
 *
 * See also native_acos().
 */
extern float __attribute__((const, overloadable))
    acos(float v);

extern float2 __attribute__((const, overloadable))
    acos(float2 v);

extern float3 __attribute__((const, overloadable))
    acos(float3 v);

extern float4 __attribute__((const, overloadable))
    acos(float4 v);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    acos(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    acos(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    acos(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    acos(half4 v);
#endif

/*
 * acosh: Inverse hyperbolic cosine
 *
 * Returns the inverse hyperbolic cosine, in radians.
 *
 * See also native_acosh().
 */
extern float __attribute__((const, overloadable))
    acosh(float v);

extern float2 __attribute__((const, overloadable))
    acosh(float2 v);

extern float3 __attribute__((const, overloadable))
    acosh(float3 v);

extern float4 __attribute__((const, overloadable))
    acosh(float4 v);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    acosh(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    acosh(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    acosh(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    acosh(half4 v);
#endif

/*
 * acospi: Inverse cosine divided by pi
 *
 * Returns the inverse cosine in radians, divided by pi.
 *
 * To get an inverse cosine measured in degrees, use acospi(a) * 180.f.
 *
 * See also native_acospi().
 */
extern float __attribute__((const, overloadable))
    acospi(float v);

extern float2 __attribute__((const, overloadable))
    acospi(float2 v);

extern float3 __attribute__((const, overloadable))
    acospi(float3 v);

extern float4 __attribute__((const, overloadable))
    acospi(float4 v);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    acospi(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    acospi(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    acospi(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    acospi(half4 v);
#endif

/*
 * asin: Inverse sine
 *
 * Returns the inverse sine, in radians.
 *
 * See also native_asin().
 */
extern float __attribute__((const, overloadable))
    asin(float v);

extern float2 __attribute__((const, overloadable))
    asin(float2 v);

extern float3 __attribute__((const, overloadable))
    asin(float3 v);

extern float4 __attribute__((const, overloadable))
    asin(float4 v);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    asin(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    asin(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    asin(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    asin(half4 v);
#endif

/*
 * asinh: Inverse hyperbolic sine
 *
 * Returns the inverse hyperbolic sine, in radians.
 *
 * See also native_asinh().
 */
extern float __attribute__((const, overloadable))
    asinh(float v);

extern float2 __attribute__((const, overloadable))
    asinh(float2 v);

extern float3 __attribute__((const, overloadable))
    asinh(float3 v);

extern float4 __attribute__((const, overloadable))
    asinh(float4 v);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    asinh(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    asinh(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    asinh(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    asinh(half4 v);
#endif

/*
 * asinpi: Inverse sine divided by pi
 *
 * Returns the inverse sine in radians, divided by pi.
 *
 * To get an inverse sine measured in degrees, use asinpi(a) * 180.f.
 *
 * See also native_asinpi().
 */
extern float __attribute__((const, overloadable))
    asinpi(float v);

extern float2 __attribute__((const, overloadable))
    asinpi(float2 v);

extern float3 __attribute__((const, overloadable))
    asinpi(float3 v);

extern float4 __attribute__((const, overloadable))
    asinpi(float4 v);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    asinpi(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    asinpi(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    asinpi(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    asinpi(half4 v);
#endif

/*
 * atan: Inverse tangent
 *
 * Returns the inverse tangent, in radians.
 *
 * See also native_atan().
 */
extern float __attribute__((const, overloadable))
    atan(float v);

extern float2 __attribute__((const, overloadable))
    atan(float2 v);

extern float3 __attribute__((const, overloadable))
    atan(float3 v);

extern float4 __attribute__((const, overloadable))
    atan(float4 v);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    atan(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    atan(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    atan(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    atan(half4 v);
#endif

/*
 * atan2: Inverse tangent of a ratio
 *
 * Returns the inverse tangent of (numerator / denominator), in radians.
 *
 * See also native_atan2().
 *
 * Parameters:
 *   numerator: Numerator.
 *   denominator: Denominator.  Can be 0.
 */
extern float __attribute__((const, overloadable))
    atan2(float numerator, float denominator);

extern float2 __attribute__((const, overloadable))
    atan2(float2 numerator, float2 denominator);

extern float3 __attribute__((const, overloadable))
    atan2(float3 numerator, float3 denominator);

extern float4 __attribute__((const, overloadable))
    atan2(float4 numerator, float4 denominator);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    atan2(half numerator, half denominator);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    atan2(half2 numerator, half2 denominator);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    atan2(half3 numerator, half3 denominator);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    atan2(half4 numerator, half4 denominator);
#endif

/*
 * atan2pi: Inverse tangent of a ratio, divided by pi
 *
 * Returns the inverse tangent of (numerator / denominator), in radians, divided by pi.
 *
 * To get an inverse tangent measured in degrees, use atan2pi(n, d) * 180.f.
 *
 * See also native_atan2pi().
 *
 * Parameters:
 *   numerator: Numerator.
 *   denominator: Denominator.  Can be 0.
 */
extern float __attribute__((const, overloadable))
    atan2pi(float numerator, float denominator);

extern float2 __attribute__((const, overloadable))
    atan2pi(float2 numerator, float2 denominator);

extern float3 __attribute__((const, overloadable))
    atan2pi(float3 numerator, float3 denominator);

extern float4 __attribute__((const, overloadable))
    atan2pi(float4 numerator, float4 denominator);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    atan2pi(half numerator, half denominator);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    atan2pi(half2 numerator, half2 denominator);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    atan2pi(half3 numerator, half3 denominator);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    atan2pi(half4 numerator, half4 denominator);
#endif

/*
 * atanh: Inverse hyperbolic tangent
 *
 * Returns the inverse hyperbolic tangent, in radians.
 *
 * See also native_atanh().
 */
extern float __attribute__((const, overloadable))
    atanh(float v);

extern float2 __attribute__((const, overloadable))
    atanh(float2 v);

extern float3 __attribute__((const, overloadable))
    atanh(float3 v);

extern float4 __attribute__((const, overloadable))
    atanh(float4 v);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    atanh(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    atanh(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    atanh(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    atanh(half4 v);
#endif

/*
 * atanpi: Inverse tangent divided by pi
 *
 * Returns the inverse tangent in radians, divided by pi.
 *
 * To get an inverse tangent measured in degrees, use atanpi(a) * 180.f.
 *
 * See also native_atanpi().
 */
extern float __attribute__((const, overloadable))
    atanpi(float v);

extern float2 __attribute__((const, overloadable))
    atanpi(float2 v);

extern float3 __attribute__((const, overloadable))
    atanpi(float3 v);

extern float4 __attribute__((const, overloadable))
    atanpi(float4 v);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    atanpi(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    atanpi(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    atanpi(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    atanpi(half4 v);
#endif

/*
 * cbrt: Cube root
 *
 * Returns the cube root.
 *
 * See also native_cbrt().
 */
extern float __attribute__((const, overloadable))
    cbrt(float v);

extern float2 __attribute__((const, overloadable))
    cbrt(float2 v);

extern float3 __attribute__((const, overloadable))
    cbrt(float3 v);

extern float4 __attribute__((const, overloadable))
    cbrt(float4 v);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    cbrt(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    cbrt(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    cbrt(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    cbrt(half4 v);
#endif

/*
 * ceil: Smallest integer not less than a value
 *
 * Returns the smallest integer not less than a value.
 *
 * For example, ceil(1.2f) returns 2.f, and ceil(-1.2f) returns -1.f.
 *
 * See also floor().
 */
extern float __attribute__((const, overloadable))
    ceil(float v);

extern float2 __attribute__((const, overloadable))
    ceil(float2 v);

extern float3 __attribute__((const, overloadable))
    ceil(float3 v);

extern float4 __attribute__((const, overloadable))
    ceil(float4 v);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    ceil(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    ceil(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    ceil(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    ceil(half4 v);
#endif

/*
 * clamp: Restrain a value to a range
 *
 * Clamps a value to a specified high and low bound.  clamp() returns min_value
 * if value < min_value, max_value if value > max_value, otherwise value.
 *
 * There are two variants of clamp: one where the min and max are scalars applied
 * to all entries of the value, the other where the min and max are also vectors.
 *
 * If min_value is greater than max_value, the results are undefined.
 *
 * Parameters:
 *   value: Value to be clamped.
 *   min_value: Lower bound, a scalar or matching vector.
 *   max_value: High bound, must match the type of low.
 */
extern float __attribute__((const, overloadable))
    clamp(float value, float min_value, float max_value);

extern float2 __attribute__((const, overloadable))
    clamp(float2 value, float2 min_value, float2 max_value);

extern float3 __attribute__((const, overloadable))
    clamp(float3 value, float3 min_value, float3 max_value);

extern float4 __attribute__((const, overloadable))
    clamp(float4 value, float4 min_value, float4 max_value);

extern float2 __attribute__((const, overloadable))
    clamp(float2 value, float min_value, float max_value);

extern float3 __attribute__((const, overloadable))
    clamp(float3 value, float min_value, float max_value);

extern float4 __attribute__((const, overloadable))
    clamp(float4 value, float min_value, float max_value);

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern char __attribute__((const, overloadable))
    clamp(char value, char min_value, char max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern char2 __attribute__((const, overloadable))
    clamp(char2 value, char2 min_value, char2 max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern char3 __attribute__((const, overloadable))
    clamp(char3 value, char3 min_value, char3 max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern char4 __attribute__((const, overloadable))
    clamp(char4 value, char4 min_value, char4 max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern uchar __attribute__((const, overloadable))
    clamp(uchar value, uchar min_value, uchar max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern uchar2 __attribute__((const, overloadable))
    clamp(uchar2 value, uchar2 min_value, uchar2 max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern uchar3 __attribute__((const, overloadable))
    clamp(uchar3 value, uchar3 min_value, uchar3 max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern uchar4 __attribute__((const, overloadable))
    clamp(uchar4 value, uchar4 min_value, uchar4 max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern short __attribute__((const, overloadable))
    clamp(short value, short min_value, short max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern short2 __attribute__((const, overloadable))
    clamp(short2 value, short2 min_value, short2 max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern short3 __attribute__((const, overloadable))
    clamp(short3 value, short3 min_value, short3 max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern short4 __attribute__((const, overloadable))
    clamp(short4 value, short4 min_value, short4 max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern ushort __attribute__((const, overloadable))
    clamp(ushort value, ushort min_value, ushort max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern ushort2 __attribute__((const, overloadable))
    clamp(ushort2 value, ushort2 min_value, ushort2 max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern ushort3 __attribute__((const, overloadable))
    clamp(ushort3 value, ushort3 min_value, ushort3 max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern ushort4 __attribute__((const, overloadable))
    clamp(ushort4 value, ushort4 min_value, ushort4 max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern int __attribute__((const, overloadable))
    clamp(int value, int min_value, int max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern int2 __attribute__((const, overloadable))
    clamp(int2 value, int2 min_value, int2 max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern int3 __attribute__((const, overloadable))
    clamp(int3 value, int3 min_value, int3 max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern int4 __attribute__((const, overloadable))
    clamp(int4 value, int4 min_value, int4 max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern uint __attribute__((const, overloadable))
    clamp(uint value, uint min_value, uint max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern uint2 __attribute__((const, overloadable))
    clamp(uint2 value, uint2 min_value, uint2 max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern uint3 __attribute__((const, overloadable))
    clamp(uint3 value, uint3 min_value, uint3 max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern uint4 __attribute__((const, overloadable))
    clamp(uint4 value, uint4 min_value, uint4 max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern long __attribute__((const, overloadable))
    clamp(long value, long min_value, long max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern long2 __attribute__((const, overloadable))
    clamp(long2 value, long2 min_value, long2 max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern long3 __attribute__((const, overloadable))
    clamp(long3 value, long3 min_value, long3 max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern long4 __attribute__((const, overloadable))
    clamp(long4 value, long4 min_value, long4 max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern ulong __attribute__((const, overloadable))
    clamp(ulong value, ulong min_value, ulong max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern ulong2 __attribute__((const, overloadable))
    clamp(ulong2 value, ulong2 min_value, ulong2 max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern ulong3 __attribute__((const, overloadable))
    clamp(ulong3 value, ulong3 min_value, ulong3 max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern ulong4 __attribute__((const, overloadable))
    clamp(ulong4 value, ulong4 min_value, ulong4 max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern char2 __attribute__((const, overloadable))
    clamp(char2 value, char min_value, char max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern char3 __attribute__((const, overloadable))
    clamp(char3 value, char min_value, char max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern char4 __attribute__((const, overloadable))
    clamp(char4 value, char min_value, char max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern uchar2 __attribute__((const, overloadable))
    clamp(uchar2 value, uchar min_value, uchar max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern uchar3 __attribute__((const, overloadable))
    clamp(uchar3 value, uchar min_value, uchar max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern uchar4 __attribute__((const, overloadable))
    clamp(uchar4 value, uchar min_value, uchar max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern short2 __attribute__((const, overloadable))
    clamp(short2 value, short min_value, short max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern short3 __attribute__((const, overloadable))
    clamp(short3 value, short min_value, short max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern short4 __attribute__((const, overloadable))
    clamp(short4 value, short min_value, short max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern ushort2 __attribute__((const, overloadable))
    clamp(ushort2 value, ushort min_value, ushort max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern ushort3 __attribute__((const, overloadable))
    clamp(ushort3 value, ushort min_value, ushort max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern ushort4 __attribute__((const, overloadable))
    clamp(ushort4 value, ushort min_value, ushort max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern int2 __attribute__((const, overloadable))
    clamp(int2 value, int min_value, int max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern int3 __attribute__((const, overloadable))
    clamp(int3 value, int min_value, int max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern int4 __attribute__((const, overloadable))
    clamp(int4 value, int min_value, int max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern uint2 __attribute__((const, overloadable))
    clamp(uint2 value, uint min_value, uint max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern uint3 __attribute__((const, overloadable))
    clamp(uint3 value, uint min_value, uint max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern uint4 __attribute__((const, overloadable))
    clamp(uint4 value, uint min_value, uint max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern long2 __attribute__((const, overloadable))
    clamp(long2 value, long min_value, long max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern long3 __attribute__((const, overloadable))
    clamp(long3 value, long min_value, long max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern long4 __attribute__((const, overloadable))
    clamp(long4 value, long min_value, long max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern ulong2 __attribute__((const, overloadable))
    clamp(ulong2 value, ulong min_value, ulong max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern ulong3 __attribute__((const, overloadable))
    clamp(ulong3 value, ulong min_value, ulong max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 19))
extern ulong4 __attribute__((const, overloadable))
    clamp(ulong4 value, ulong min_value, ulong max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    clamp(half value, half min_value, half max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    clamp(half2 value, half2 min_value, half2 max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    clamp(half3 value, half3 min_value, half3 max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    clamp(half4 value, half4 min_value, half4 max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    clamp(half2 value, half min_value, half max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    clamp(half3 value, half min_value, half max_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    clamp(half4 value, half min_value, half max_value);
#endif

/*
 * clz: Number of leading 0 bits
 *
 * Returns the number of leading 0-bits in a value.
 *
 * For example, clz((char)0x03) returns 6.
 */
extern char __attribute__((const, overloadable))
    clz(char value);

extern char2 __attribute__((const, overloadable))
    clz(char2 value);

extern char3 __attribute__((const, overloadable))
    clz(char3 value);

extern char4 __attribute__((const, overloadable))
    clz(char4 value);

extern uchar __attribute__((const, overloadable))
    clz(uchar value);

extern uchar2 __attribute__((const, overloadable))
    clz(uchar2 value);

extern uchar3 __attribute__((const, overloadable))
    clz(uchar3 value);

extern uchar4 __attribute__((const, overloadable))
    clz(uchar4 value);

extern short __attribute__((const, overloadable))
    clz(short value);

extern short2 __attribute__((const, overloadable))
    clz(short2 value);

extern short3 __attribute__((const, overloadable))
    clz(short3 value);

extern short4 __attribute__((const, overloadable))
    clz(short4 value);

extern ushort __attribute__((const, overloadable))
    clz(ushort value);

extern ushort2 __attribute__((const, overloadable))
    clz(ushort2 value);

extern ushort3 __attribute__((const, overloadable))
    clz(ushort3 value);

extern ushort4 __attribute__((const, overloadable))
    clz(ushort4 value);

extern int __attribute__((const, overloadable))
    clz(int value);

extern int2 __attribute__((const, overloadable))
    clz(int2 value);

extern int3 __attribute__((const, overloadable))
    clz(int3 value);

extern int4 __attribute__((const, overloadable))
    clz(int4 value);

extern uint __attribute__((const, overloadable))
    clz(uint value);

extern uint2 __attribute__((const, overloadable))
    clz(uint2 value);

extern uint3 __attribute__((const, overloadable))
    clz(uint3 value);

extern uint4 __attribute__((const, overloadable))
    clz(uint4 value);

/*
 * copysign: Copies the sign of a number to another
 *
 * Copies the sign from sign_value to magnitude_value.
 *
 * The value returned is either magnitude_value or -magnitude_value.
 *
 * For example, copysign(4.0f, -2.7f) returns -4.0f and copysign(-4.0f, 2.7f) returns 4.0f.
 */
extern float __attribute__((const, overloadable))
    copysign(float magnitude_value, float sign_value);

extern float2 __attribute__((const, overloadable))
    copysign(float2 magnitude_value, float2 sign_value);

extern float3 __attribute__((const, overloadable))
    copysign(float3 magnitude_value, float3 sign_value);

extern float4 __attribute__((const, overloadable))
    copysign(float4 magnitude_value, float4 sign_value);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    copysign(half magnitude_value, half sign_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    copysign(half2 magnitude_value, half2 sign_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    copysign(half3 magnitude_value, half3 sign_value);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    copysign(half4 magnitude_value, half4 sign_value);
#endif

/*
 * cos: Cosine
 *
 * Returns the cosine of an angle measured in radians.
 *
 * See also native_cos().
 */
extern float __attribute__((const, overloadable))
    cos(float v);

extern float2 __attribute__((const, overloadable))
    cos(float2 v);

extern float3 __attribute__((const, overloadable))
    cos(float3 v);

extern float4 __attribute__((const, overloadable))
    cos(float4 v);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    cos(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    cos(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    cos(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    cos(half4 v);
#endif

/*
 * cosh: Hypebolic cosine
 *
 * Returns the hypebolic cosine of v, where v is measured in radians.
 *
 * See also native_cosh().
 */
extern float __attribute__((const, overloadable))
    cosh(float v);

extern float2 __attribute__((const, overloadable))
    cosh(float2 v);

extern float3 __attribute__((const, overloadable))
    cosh(float3 v);

extern float4 __attribute__((const, overloadable))
    cosh(float4 v);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    cosh(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    cosh(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    cosh(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    cosh(half4 v);
#endif

/*
 * cospi: Cosine of a number multiplied by pi
 *
 * Returns the cosine of (v * pi), where (v * pi) is measured in radians.
 *
 * To get the cosine of a value measured in degrees, call cospi(v / 180.f).
 *
 * See also native_cospi().
 */
extern float __attribute__((const, overloadable))
    cospi(float v);

extern float2 __attribute__((const, overloadable))
    cospi(float2 v);

extern float3 __attribute__((const, overloadable))
    cospi(float3 v);

extern float4 __attribute__((const, overloadable))
    cospi(float4 v);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    cospi(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    cospi(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    cospi(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    cospi(half4 v);
#endif

/*
 * degrees: Converts radians into degrees
 *
 * Converts from radians to degrees.
 */
extern float __attribute__((const, overloadable))
    degrees(float v);

extern float2 __attribute__((const, overloadable))
    degrees(float2 v);

extern float3 __attribute__((const, overloadable))
    degrees(float3 v);

extern float4 __attribute__((const, overloadable))
    degrees(float4 v);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    degrees(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    degrees(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    degrees(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    degrees(half4 v);
#endif

/*
 * erf: Mathematical error function
 *
 * Returns the error function.
 */
extern float __attribute__((const, overloadable))
    erf(float v);

extern float2 __attribute__((const, overloadable))
    erf(float2 v);

extern float3 __attribute__((const, overloadable))
    erf(float3 v);

extern float4 __attribute__((const, overloadable))
    erf(float4 v);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    erf(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    erf(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    erf(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    erf(half4 v);
#endif

/*
 * erfc: Mathematical complementary error function
 *
 * Returns the complementary error function.
 */
extern float __attribute__((const, overloadable))
    erfc(float v);

extern float2 __attribute__((const, overloadable))
    erfc(float2 v);

extern float3 __attribute__((const, overloadable))
    erfc(float3 v);

extern float4 __attribute__((const, overloadable))
    erfc(float4 v);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    erfc(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    erfc(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    erfc(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    erfc(half4 v);
#endif

/*
 * exp: e raised to a number
 *
 * Returns e raised to v, i.e. e ^ v.
 *
 * See also native_exp().
 */
extern float __attribute__((const, overloadable))
    exp(float v);

extern float2 __attribute__((const, overloadable))
    exp(float2 v);

extern float3 __attribute__((const, overloadable))
    exp(float3 v);

extern float4 __attribute__((const, overloadable))
    exp(float4 v);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    exp(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    exp(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    exp(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    exp(half4 v);
#endif

/*
 * exp10: 10 raised to a number
 *
 * Returns 10 raised to v, i.e. 10.f ^ v.
 *
 * See also native_exp10().
 */
extern float __attribute__((const, overloadable))
    exp10(float v);

extern float2 __attribute__((const, overloadable))
    exp10(float2 v);

extern float3 __attribute__((const, overloadable))
    exp10(float3 v);

extern float4 __attribute__((const, overloadable))
    exp10(float4 v);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    exp10(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    exp10(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    exp10(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    exp10(half4 v);
#endif

/*
 * exp2: 2 raised to a number
 *
 * Returns 2 raised to v, i.e. 2.f ^ v.
 *
 * See also native_exp2().
 */
extern float __attribute__((const, overloadable))
    exp2(float v);

extern float2 __attribute__((const, overloadable))
    exp2(float2 v);

extern float3 __attribute__((const, overloadable))
    exp2(float3 v);

extern float4 __attribute__((const, overloadable))
    exp2(float4 v);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    exp2(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    exp2(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    exp2(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    exp2(half4 v);
#endif

/*
 * expm1: e raised to a number minus one
 *
 * Returns e raised to v minus 1, i.e. (e ^ v) - 1.
 *
 * See also native_expm1().
 */
extern float __attribute__((const, overloadable))
    expm1(float v);

extern float2 __attribute__((const, overloadable))
    expm1(float2 v);

extern float3 __attribute__((const, overloadable))
    expm1(float3 v);

extern float4 __attribute__((const, overloadable))
    expm1(float4 v);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    expm1(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    expm1(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    expm1(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    expm1(half4 v);
#endif

/*
 * fabs: Absolute value of a float
 *
 * Returns the absolute value of the float v.
 *
 * For integers, use abs().
 */
extern float __attribute__((const, overloadable))
    fabs(float v);

extern float2 __attribute__((const, overloadable))
    fabs(float2 v);

extern float3 __attribute__((const, overloadable))
    fabs(float3 v);

extern float4 __attribute__((const, overloadable))
    fabs(float4 v);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    fabs(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    fabs(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    fabs(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    fabs(half4 v);
#endif

/*
 * fdim: Positive difference between two values
 *
 * Returns the positive difference between two values.
 *
 * If a > b, returns (a - b) otherwise returns 0f.
 */
extern float __attribute__((const, overloadable))
    fdim(float a, float b);

extern float2 __attribute__((const, overloadable))
    fdim(float2 a, float2 b);

extern float3 __attribute__((const, overloadable))
    fdim(float3 a, float3 b);

extern float4 __attribute__((const, overloadable))
    fdim(float4 a, float4 b);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    fdim(half a, half b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    fdim(half2 a, half2 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    fdim(half3 a, half3 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    fdim(half4 a, half4 b);
#endif

/*
 * floor: Smallest integer not greater than a value
 *
 * Returns the smallest integer not greater than a value.
 *
 * For example, floor(1.2f) returns 1.f, and floor(-1.2f) returns -2.f.
 *
 * See also ceil().
 */
extern float __attribute__((const, overloadable))
    floor(float v);

extern float2 __attribute__((const, overloadable))
    floor(float2 v);

extern float3 __attribute__((const, overloadable))
    floor(float3 v);

extern float4 __attribute__((const, overloadable))
    floor(float4 v);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    floor(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    floor(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    floor(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    floor(half4 v);
#endif

/*
 * fma: Multiply and add
 *
 * Multiply and add.  Returns (multiplicand1 * multiplicand2) + offset.
 *
 * This function is similar to mad().  fma() retains full precision of the multiplied result
 * and rounds only after the addition.  mad() rounds after the multiplication and the addition.
 * This extra precision is not guaranteed in rs_fp_relaxed mode.
 */
extern float __attribute__((const, overloadable))
    fma(float multiplicand1, float multiplicand2, float offset);

extern float2 __attribute__((const, overloadable))
    fma(float2 multiplicand1, float2 multiplicand2, float2 offset);

extern float3 __attribute__((const, overloadable))
    fma(float3 multiplicand1, float3 multiplicand2, float3 offset);

extern float4 __attribute__((const, overloadable))
    fma(float4 multiplicand1, float4 multiplicand2, float4 offset);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    fma(half multiplicand1, half multiplicand2, half offset);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    fma(half2 multiplicand1, half2 multiplicand2, half2 offset);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    fma(half3 multiplicand1, half3 multiplicand2, half3 offset);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    fma(half4 multiplicand1, half4 multiplicand2, half4 offset);
#endif

/*
 * fmax: Maximum of two floats
 *
 * Returns the maximum of a and b, i.e. (a < b ? b : a).
 *
 * The max() function returns identical results but can be applied to more data types.
 */
extern float __attribute__((const, overloadable))
    fmax(float a, float b);

extern float2 __attribute__((const, overloadable))
    fmax(float2 a, float2 b);

extern float3 __attribute__((const, overloadable))
    fmax(float3 a, float3 b);

extern float4 __attribute__((const, overloadable))
    fmax(float4 a, float4 b);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    fmax(half a, half b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    fmax(half2 a, half2 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    fmax(half3 a, half3 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    fmax(half4 a, half4 b);
#endif

extern float2 __attribute__((const, overloadable))
    fmax(float2 a, float b);

extern float3 __attribute__((const, overloadable))
    fmax(float3 a, float b);

extern float4 __attribute__((const, overloadable))
    fmax(float4 a, float b);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    fmax(half2 a, half b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    fmax(half3 a, half b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    fmax(half4 a, half b);
#endif

/*
 * fmin: Minimum of two floats
 *
 * Returns the minimum of a and b, i.e. (a > b ? b : a).
 *
 * The min() function returns identical results but can be applied to more data types.
 */
extern float __attribute__((const, overloadable))
    fmin(float a, float b);

extern float2 __attribute__((const, overloadable))
    fmin(float2 a, float2 b);

extern float3 __attribute__((const, overloadable))
    fmin(float3 a, float3 b);

extern float4 __attribute__((const, overloadable))
    fmin(float4 a, float4 b);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    fmin(half a, half b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    fmin(half2 a, half2 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    fmin(half3 a, half3 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    fmin(half4 a, half4 b);
#endif

extern float2 __attribute__((const, overloadable))
    fmin(float2 a, float b);

extern float3 __attribute__((const, overloadable))
    fmin(float3 a, float b);

extern float4 __attribute__((const, overloadable))
    fmin(float4 a, float b);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    fmin(half2 a, half b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    fmin(half3 a, half b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    fmin(half4 a, half b);
#endif

/*
 * fmod: Modulo
 *
 * Returns the remainder of (numerator / denominator), where the quotient is rounded towards zero.
 *
 * The function remainder() is similar but rounds toward the closest interger.
 * For example, fmod(-3.8f, 2.f) returns -1.8f (-3.8f - -1.f * 2.f)
 * while remainder(-3.8f, 2.f) returns 0.2f (-3.8f - -2.f * 2.f).
 */
extern float __attribute__((const, overloadable))
    fmod(float numerator, float denominator);

extern float2 __attribute__((const, overloadable))
    fmod(float2 numerator, float2 denominator);

extern float3 __attribute__((const, overloadable))
    fmod(float3 numerator, float3 denominator);

extern float4 __attribute__((const, overloadable))
    fmod(float4 numerator, float4 denominator);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    fmod(half numerator, half denominator);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    fmod(half2 numerator, half2 denominator);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    fmod(half3 numerator, half3 denominator);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    fmod(half4 numerator, half4 denominator);
#endif

/*
 * fract: Positive fractional part
 *
 * Returns the positive fractional part of v, i.e. v - floor(v).
 *
 * For example, fract(1.3f, &val) returns 0.3f and sets val to 1.f.
 * fract(-1.3f, &val) returns 0.7f and sets val to -2.f.
 *
 * Parameters:
 *   v: Input value.
 *   floor: If floor is not null, *floor will be set to the floor of v.
 */
extern float __attribute__((overloadable))
    fract(float v, float* floor);

extern float2 __attribute__((overloadable))
    fract(float2 v, float2* floor);

extern float3 __attribute__((overloadable))
    fract(float3 v, float3* floor);

extern float4 __attribute__((overloadable))
    fract(float4 v, float4* floor);

#if !defined(RS_VERSION) || (RS_VERSION <= 23)
static inline float __attribute__((const, overloadable))
    fract(float v) {
    float unused;
    return fract(v, &unused);
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 23)
static inline float2 __attribute__((const, overloadable))
    fract(float2 v) {
    float2 unused;
    return fract(v, &unused);
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 23)
static inline float3 __attribute__((const, overloadable))
    fract(float3 v) {
    float3 unused;
    return fract(v, &unused);
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 23)
static inline float4 __attribute__((const, overloadable))
    fract(float4 v) {
    float4 unused;
    return fract(v, &unused);
}
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern float __attribute__((overloadable))
    fract(float v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern float2 __attribute__((overloadable))
    fract(float2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern float3 __attribute__((overloadable))
    fract(float3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern float4 __attribute__((overloadable))
    fract(float4 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((overloadable))
    fract(half v, half* floor);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((overloadable))
    fract(half2 v, half2* floor);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((overloadable))
    fract(half3 v, half3* floor);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((overloadable))
    fract(half4 v, half4* floor);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((overloadable))
    fract(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((overloadable))
    fract(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((overloadable))
    fract(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((overloadable))
    fract(half4 v);
#endif

/*
 * frexp: Binary mantissa and exponent
 *
 * Returns the binary mantissa and exponent of v, i.e. v == mantissa * 2 ^ exponent.
 *
 * The mantissa is always between 0.5 (inclusive) and 1.0 (exclusive).
 *
 * See ldexp() for the reverse operation.  See also logb() and ilogb().
 *
 * Parameters:
 *   v: Input value.
 *   exponent: If exponent is not null, *exponent will be set to the exponent of v.
 */
extern float __attribute__((overloadable))
    frexp(float v, int* exponent);

extern float2 __attribute__((overloadable))
    frexp(float2 v, int2* exponent);

extern float3 __attribute__((overloadable))
    frexp(float3 v, int3* exponent);

extern float4 __attribute__((overloadable))
    frexp(float4 v, int4* exponent);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((overloadable))
    frexp(half v, int* exponent);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((overloadable))
    frexp(half2 v, int2* exponent);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((overloadable))
    frexp(half3 v, int3* exponent);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((overloadable))
    frexp(half4 v, int4* exponent);
#endif

/*
 * half_recip: Reciprocal computed to 16 bit precision
 *
 * Returns the approximate reciprocal of a value.
 *
 * The precision is that of a 16 bit floating point value.
 *
 * See also native_recip().
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 17))
extern float __attribute__((const, overloadable))
    half_recip(float v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 17))
extern float2 __attribute__((const, overloadable))
    half_recip(float2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 17))
extern float3 __attribute__((const, overloadable))
    half_recip(float3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 17))
extern float4 __attribute__((const, overloadable))
    half_recip(float4 v);
#endif

/*
 * half_rsqrt: Reciprocal of a square root computed to 16 bit precision
 *
 * Returns the approximate value of (1.f / sqrt(value)).
 *
 * The precision is that of a 16 bit floating point value.
 *
 * See also rsqrt(), native_rsqrt().
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 17))
extern float __attribute__((const, overloadable))
    half_rsqrt(float v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 17))
extern float2 __attribute__((const, overloadable))
    half_rsqrt(float2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 17))
extern float3 __attribute__((const, overloadable))
    half_rsqrt(float3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 17))
extern float4 __attribute__((const, overloadable))
    half_rsqrt(float4 v);
#endif

/*
 * half_sqrt: Square root computed to 16 bit precision
 *
 * Returns the approximate square root of a value.
 *
 * The precision is that of a 16 bit floating point value.
 *
 * See also sqrt(), native_sqrt().
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 17))
extern float __attribute__((const, overloadable))
    half_sqrt(float v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 17))
extern float2 __attribute__((const, overloadable))
    half_sqrt(float2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 17))
extern float3 __attribute__((const, overloadable))
    half_sqrt(float3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 17))
extern float4 __attribute__((const, overloadable))
    half_sqrt(float4 v);
#endif

/*
 * hypot: Hypotenuse
 *
 * Returns the hypotenuse, i.e. sqrt(a * a + b * b).
 *
 * See also native_hypot().
 */
extern float __attribute__((const, overloadable))
    hypot(float a, float b);

extern float2 __attribute__((const, overloadable))
    hypot(float2 a, float2 b);

extern float3 __attribute__((const, overloadable))
    hypot(float3 a, float3 b);

extern float4 __attribute__((const, overloadable))
    hypot(float4 a, float4 b);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    hypot(half a, half b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    hypot(half2 a, half2 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    hypot(half3 a, half3 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    hypot(half4 a, half4 b);
#endif

/*
 * ilogb: Base two exponent
 *
 * Returns the base two exponent of a value, where the mantissa is between
 * 1.f (inclusive) and 2.f (exclusive).
 *
 * For example, ilogb(8.5f) returns 3.
 *
 * Because of the difference in mantissa, this number is one less than is returned by frexp().
 *
 * logb() is similar but returns a float.
 */
extern int __attribute__((const, overloadable))
    ilogb(float v);

extern int2 __attribute__((const, overloadable))
    ilogb(float2 v);

extern int3 __attribute__((const, overloadable))
    ilogb(float3 v);

extern int4 __attribute__((const, overloadable))
    ilogb(float4 v);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern int __attribute__((const, overloadable))
    ilogb(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern int2 __attribute__((const, overloadable))
    ilogb(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern int3 __attribute__((const, overloadable))
    ilogb(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern int4 __attribute__((const, overloadable))
    ilogb(half4 v);
#endif

/*
 * ldexp: Creates a floating point from mantissa and exponent
 *
 * Returns the floating point created from the mantissa and exponent,
 * i.e. (mantissa * 2 ^ exponent).
 *
 * See frexp() for the reverse operation.
 *
 * Parameters:
 *   mantissa: Mantissa.
 *   exponent: Exponent, a single component or matching vector.
 */
extern float __attribute__((const, overloadable))
    ldexp(float mantissa, int exponent);

extern float2 __attribute__((const, overloadable))
    ldexp(float2 mantissa, int2 exponent);

extern float3 __attribute__((const, overloadable))
    ldexp(float3 mantissa, int3 exponent);

extern float4 __attribute__((const, overloadable))
    ldexp(float4 mantissa, int4 exponent);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    ldexp(half mantissa, int exponent);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    ldexp(half2 mantissa, int2 exponent);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    ldexp(half3 mantissa, int3 exponent);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    ldexp(half4 mantissa, int4 exponent);
#endif

extern float2 __attribute__((const, overloadable))
    ldexp(float2 mantissa, int exponent);

extern float3 __attribute__((const, overloadable))
    ldexp(float3 mantissa, int exponent);

extern float4 __attribute__((const, overloadable))
    ldexp(float4 mantissa, int exponent);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    ldexp(half2 mantissa, int exponent);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    ldexp(half3 mantissa, int exponent);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    ldexp(half4 mantissa, int exponent);
#endif

/*
 * lgamma: Natural logarithm of the gamma function
 *
 * Returns the natural logarithm of the absolute value of the gamma function,
 * i.e. log(fabs(tgamma(v))).
 *
 * See also tgamma().
 *
 * Parameters:
 *   sign_of_gamma: If sign_of_gamma is not null, *sign_of_gamma will be set to -1.f if the gamma of v is negative, otherwise to 1.f.
 */
extern float __attribute__((const, overloadable))
    lgamma(float v);

extern float2 __attribute__((const, overloadable))
    lgamma(float2 v);

extern float3 __attribute__((const, overloadable))
    lgamma(float3 v);

extern float4 __attribute__((const, overloadable))
    lgamma(float4 v);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    lgamma(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    lgamma(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    lgamma(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    lgamma(half4 v);
#endif

extern float __attribute__((overloadable))
    lgamma(float v, int* sign_of_gamma);

extern float2 __attribute__((overloadable))
    lgamma(float2 v, int2* sign_of_gamma);

extern float3 __attribute__((overloadable))
    lgamma(float3 v, int3* sign_of_gamma);

extern float4 __attribute__((overloadable))
    lgamma(float4 v, int4* sign_of_gamma);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((overloadable))
    lgamma(half v, int* sign_of_gamma);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((overloadable))
    lgamma(half2 v, int2* sign_of_gamma);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((overloadable))
    lgamma(half3 v, int3* sign_of_gamma);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((overloadable))
    lgamma(half4 v, int4* sign_of_gamma);
#endif

/*
 * log: Natural logarithm
 *
 * Returns the natural logarithm.
 *
 * See also native_log().
 */
extern float __attribute__((const, overloadable))
    log(float v);

extern float2 __attribute__((const, overloadable))
    log(float2 v);

extern float3 __attribute__((const, overloadable))
    log(float3 v);

extern float4 __attribute__((const, overloadable))
    log(float4 v);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    log(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    log(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    log(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    log(half4 v);
#endif

/*
 * log10: Base 10 logarithm
 *
 * Returns the base 10 logarithm.
 *
 * See also native_log10().
 */
extern float __attribute__((const, overloadable))
    log10(float v);

extern float2 __attribute__((const, overloadable))
    log10(float2 v);

extern float3 __attribute__((const, overloadable))
    log10(float3 v);

extern float4 __attribute__((const, overloadable))
    log10(float4 v);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    log10(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    log10(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    log10(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    log10(half4 v);
#endif

/*
 * log1p: Natural logarithm of a value plus 1
 *
 * Returns the natural logarithm of (v + 1.f).
 *
 * See also native_log1p().
 */
extern float __attribute__((const, overloadable))
    log1p(float v);

extern float2 __attribute__((const, overloadable))
    log1p(float2 v);

extern float3 __attribute__((const, overloadable))
    log1p(float3 v);

extern float4 __attribute__((const, overloadable))
    log1p(float4 v);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    log1p(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    log1p(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    log1p(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    log1p(half4 v);
#endif

/*
 * log2: Base 2 logarithm
 *
 * Returns the base 2 logarithm.
 *
 * See also native_log2().
 */
extern float __attribute__((const, overloadable))
    log2(float v);

extern float2 __attribute__((const, overloadable))
    log2(float2 v);

extern float3 __attribute__((const, overloadable))
    log2(float3 v);

extern float4 __attribute__((const, overloadable))
    log2(float4 v);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    log2(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    log2(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    log2(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    log2(half4 v);
#endif

/*
 * logb: Base two exponent
 *
 * Returns the base two exponent of a value, where the mantissa is between
 * 1.f (inclusive) and 2.f (exclusive).
 *
 * For example, logb(8.5f) returns 3.f.
 *
 * Because of the difference in mantissa, this number is one less than is returned by frexp().
 *
 * ilogb() is similar but returns an integer.
 */
extern float __attribute__((const, overloadable))
    logb(float v);

extern float2 __attribute__((const, overloadable))
    logb(float2 v);

extern float3 __attribute__((const, overloadable))
    logb(float3 v);

extern float4 __attribute__((const, overloadable))
    logb(float4 v);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    logb(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    logb(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    logb(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    logb(half4 v);
#endif

/*
 * mad: Multiply and add
 *
 * Multiply and add.  Returns (multiplicand1 * multiplicand2) + offset.
 *
 * This function is similar to fma().  fma() retains full precision of the multiplied result
 * and rounds only after the addition.  mad() rounds after the multiplication and the addition.
 * In rs_fp_relaxed mode, mad() may not do the rounding after multiplicaiton.
 */
extern float __attribute__((const, overloadable))
    mad(float multiplicand1, float multiplicand2, float offset);

extern float2 __attribute__((const, overloadable))
    mad(float2 multiplicand1, float2 multiplicand2, float2 offset);

extern float3 __attribute__((const, overloadable))
    mad(float3 multiplicand1, float3 multiplicand2, float3 offset);

extern float4 __attribute__((const, overloadable))
    mad(float4 multiplicand1, float4 multiplicand2, float4 offset);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    mad(half multiplicand1, half multiplicand2, half offset);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    mad(half2 multiplicand1, half2 multiplicand2, half2 offset);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    mad(half3 multiplicand1, half3 multiplicand2, half3 offset);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    mad(half4 multiplicand1, half4 multiplicand2, half4 offset);
#endif

/*
 * max: Maximum
 *
 * Returns the maximum value of two arguments.
 */
extern float __attribute__((const, overloadable))
    max(float a, float b);

extern float2 __attribute__((const, overloadable))
    max(float2 a, float2 b);

extern float3 __attribute__((const, overloadable))
    max(float3 a, float3 b);

extern float4 __attribute__((const, overloadable))
    max(float4 a, float4 b);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    max(half a, half b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    max(half2 a, half2 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    max(half3 a, half3 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    max(half4 a, half4 b);
#endif

extern float2 __attribute__((const, overloadable))
    max(float2 a, float b);

extern float3 __attribute__((const, overloadable))
    max(float3 a, float b);

extern float4 __attribute__((const, overloadable))
    max(float4 a, float b);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    max(half2 a, half b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    max(half3 a, half b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    max(half4 a, half b);
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 20)
static inline char __attribute__((const, overloadable))
    max(char a, char b) {
    return (a > b ? a : b);
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 20)
static inline uchar __attribute__((const, overloadable))
    max(uchar a, uchar b) {
    return (a > b ? a : b);
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 20)
static inline short __attribute__((const, overloadable))
    max(short a, short b) {
    return (a > b ? a : b);
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 20)
static inline ushort __attribute__((const, overloadable))
    max(ushort a, ushort b) {
    return (a > b ? a : b);
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 20)
static inline int __attribute__((const, overloadable))
    max(int a, int b) {
    return (a > b ? a : b);
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 20)
static inline uint __attribute__((const, overloadable))
    max(uint a, uint b) {
    return (a > b ? a : b);
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 20)
static inline char2 __attribute__((const, overloadable))
    max(char2 a, char2 b) {
    char2 tmp;
    tmp.x = (a.x > b.x ? a.x : b.x);
    tmp.y = (a.y > b.y ? a.y : b.y);
    return tmp;
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 20)
static inline uchar2 __attribute__((const, overloadable))
    max(uchar2 a, uchar2 b) {
    uchar2 tmp;
    tmp.x = (a.x > b.x ? a.x : b.x);
    tmp.y = (a.y > b.y ? a.y : b.y);
    return tmp;
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 20)
static inline short2 __attribute__((const, overloadable))
    max(short2 a, short2 b) {
    short2 tmp;
    tmp.x = (a.x > b.x ? a.x : b.x);
    tmp.y = (a.y > b.y ? a.y : b.y);
    return tmp;
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 20)
static inline ushort2 __attribute__((const, overloadable))
    max(ushort2 a, ushort2 b) {
    ushort2 tmp;
    tmp.x = (a.x > b.x ? a.x : b.x);
    tmp.y = (a.y > b.y ? a.y : b.y);
    return tmp;
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 20)
static inline int2 __attribute__((const, overloadable))
    max(int2 a, int2 b) {
    int2 tmp;
    tmp.x = (a.x > b.x ? a.x : b.x);
    tmp.y = (a.y > b.y ? a.y : b.y);
    return tmp;
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 20)
static inline uint2 __attribute__((const, overloadable))
    max(uint2 a, uint2 b) {
    uint2 tmp;
    tmp.x = (a.x > b.x ? a.x : b.x);
    tmp.y = (a.y > b.y ? a.y : b.y);
    return tmp;
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 20)
static inline char3 __attribute__((const, overloadable))
    max(char3 a, char3 b) {
    char3 tmp;
    tmp.x = (a.x > b.x ? a.x : b.x);
    tmp.y = (a.y > b.y ? a.y : b.y);
    tmp.z = (a.z > b.z ? a.z : b.z);
    return tmp;
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 20)
static inline uchar3 __attribute__((const, overloadable))
    max(uchar3 a, uchar3 b) {
    uchar3 tmp;
    tmp.x = (a.x > b.x ? a.x : b.x);
    tmp.y = (a.y > b.y ? a.y : b.y);
    tmp.z = (a.z > b.z ? a.z : b.z);
    return tmp;
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 20)
static inline short3 __attribute__((const, overloadable))
    max(short3 a, short3 b) {
    short3 tmp;
    tmp.x = (a.x > b.x ? a.x : b.x);
    tmp.y = (a.y > b.y ? a.y : b.y);
    tmp.z = (a.z > b.z ? a.z : b.z);
    return tmp;
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 20)
static inline ushort3 __attribute__((const, overloadable))
    max(ushort3 a, ushort3 b) {
    ushort3 tmp;
    tmp.x = (a.x > b.x ? a.x : b.x);
    tmp.y = (a.y > b.y ? a.y : b.y);
    tmp.z = (a.z > b.z ? a.z : b.z);
    return tmp;
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 20)
static inline int3 __attribute__((const, overloadable))
    max(int3 a, int3 b) {
    int3 tmp;
    tmp.x = (a.x > b.x ? a.x : b.x);
    tmp.y = (a.y > b.y ? a.y : b.y);
    tmp.z = (a.z > b.z ? a.z : b.z);
    return tmp;
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 20)
static inline uint3 __attribute__((const, overloadable))
    max(uint3 a, uint3 b) {
    uint3 tmp;
    tmp.x = (a.x > b.x ? a.x : b.x);
    tmp.y = (a.y > b.y ? a.y : b.y);
    tmp.z = (a.z > b.z ? a.z : b.z);
    return tmp;
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 20)
static inline char4 __attribute__((const, overloadable))
    max(char4 a, char4 b) {
    char4 tmp;
    tmp.x = (a.x > b.x ? a.x : b.x);
    tmp.y = (a.y > b.y ? a.y : b.y);
    tmp.z = (a.z > b.z ? a.z : b.z);
    tmp.w = (a.w > b.w ? a.w : b.w);
    return tmp;
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 20)
static inline uchar4 __attribute__((const, overloadable))
    max(uchar4 a, uchar4 b) {
    uchar4 tmp;
    tmp.x = (a.x > b.x ? a.x : b.x);
    tmp.y = (a.y > b.y ? a.y : b.y);
    tmp.z = (a.z > b.z ? a.z : b.z);
    tmp.w = (a.w > b.w ? a.w : b.w);
    return tmp;
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 20)
static inline short4 __attribute__((const, overloadable))
    max(short4 a, short4 b) {
    short4 tmp;
    tmp.x = (a.x > b.x ? a.x : b.x);
    tmp.y = (a.y > b.y ? a.y : b.y);
    tmp.z = (a.z > b.z ? a.z : b.z);
    tmp.w = (a.w > b.w ? a.w : b.w);
    return tmp;
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 20)
static inline ushort4 __attribute__((const, overloadable))
    max(ushort4 a, ushort4 b) {
    ushort4 tmp;
    tmp.x = (a.x > b.x ? a.x : b.x);
    tmp.y = (a.y > b.y ? a.y : b.y);
    tmp.z = (a.z > b.z ? a.z : b.z);
    tmp.w = (a.w > b.w ? a.w : b.w);
    return tmp;
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 20)
static inline int4 __attribute__((const, overloadable))
    max(int4 a, int4 b) {
    int4 tmp;
    tmp.x = (a.x > b.x ? a.x : b.x);
    tmp.y = (a.y > b.y ? a.y : b.y);
    tmp.z = (a.z > b.z ? a.z : b.z);
    tmp.w = (a.w > b.w ? a.w : b.w);
    return tmp;
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 20)
static inline uint4 __attribute__((const, overloadable))
    max(uint4 a, uint4 b) {
    uint4 tmp;
    tmp.x = (a.x > b.x ? a.x : b.x);
    tmp.y = (a.y > b.y ? a.y : b.y);
    tmp.z = (a.z > b.z ? a.z : b.z);
    tmp.w = (a.w > b.w ? a.w : b.w);
    return tmp;
}
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern char __attribute__((const, overloadable))
    max(char a, char b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern char2 __attribute__((const, overloadable))
    max(char2 a, char2 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern char3 __attribute__((const, overloadable))
    max(char3 a, char3 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern char4 __attribute__((const, overloadable))
    max(char4 a, char4 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern uchar __attribute__((const, overloadable))
    max(uchar a, uchar b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern uchar2 __attribute__((const, overloadable))
    max(uchar2 a, uchar2 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern uchar3 __attribute__((const, overloadable))
    max(uchar3 a, uchar3 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern uchar4 __attribute__((const, overloadable))
    max(uchar4 a, uchar4 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern short __attribute__((const, overloadable))
    max(short a, short b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern short2 __attribute__((const, overloadable))
    max(short2 a, short2 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern short3 __attribute__((const, overloadable))
    max(short3 a, short3 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern short4 __attribute__((const, overloadable))
    max(short4 a, short4 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern ushort __attribute__((const, overloadable))
    max(ushort a, ushort b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern ushort2 __attribute__((const, overloadable))
    max(ushort2 a, ushort2 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern ushort3 __attribute__((const, overloadable))
    max(ushort3 a, ushort3 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern ushort4 __attribute__((const, overloadable))
    max(ushort4 a, ushort4 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern int __attribute__((const, overloadable))
    max(int a, int b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern int2 __attribute__((const, overloadable))
    max(int2 a, int2 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern int3 __attribute__((const, overloadable))
    max(int3 a, int3 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern int4 __attribute__((const, overloadable))
    max(int4 a, int4 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern uint __attribute__((const, overloadable))
    max(uint a, uint b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern uint2 __attribute__((const, overloadable))
    max(uint2 a, uint2 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern uint3 __attribute__((const, overloadable))
    max(uint3 a, uint3 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern uint4 __attribute__((const, overloadable))
    max(uint4 a, uint4 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern long __attribute__((const, overloadable))
    max(long a, long b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern long2 __attribute__((const, overloadable))
    max(long2 a, long2 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern long3 __attribute__((const, overloadable))
    max(long3 a, long3 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern long4 __attribute__((const, overloadable))
    max(long4 a, long4 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern ulong __attribute__((const, overloadable))
    max(ulong a, ulong b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern ulong2 __attribute__((const, overloadable))
    max(ulong2 a, ulong2 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern ulong3 __attribute__((const, overloadable))
    max(ulong3 a, ulong3 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern ulong4 __attribute__((const, overloadable))
    max(ulong4 a, ulong4 b);
#endif

/*
 * min: Minimum
 *
 * Returns the minimum value of two arguments.
 */
extern float __attribute__((const, overloadable))
    min(float a, float b);

extern float2 __attribute__((const, overloadable))
    min(float2 a, float2 b);

extern float3 __attribute__((const, overloadable))
    min(float3 a, float3 b);

extern float4 __attribute__((const, overloadable))
    min(float4 a, float4 b);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    min(half a, half b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    min(half2 a, half2 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    min(half3 a, half3 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    min(half4 a, half4 b);
#endif

extern float2 __attribute__((const, overloadable))
    min(float2 a, float b);

extern float3 __attribute__((const, overloadable))
    min(float3 a, float b);

extern float4 __attribute__((const, overloadable))
    min(float4 a, float b);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    min(half2 a, half b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    min(half3 a, half b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    min(half4 a, half b);
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 20)
static inline char __attribute__((const, overloadable))
    min(char a, char b) {
    return (a < b ? a : b);
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 20)
static inline uchar __attribute__((const, overloadable))
    min(uchar a, uchar b) {
    return (a < b ? a : b);
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 20)
static inline short __attribute__((const, overloadable))
    min(short a, short b) {
    return (a < b ? a : b);
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 20)
static inline ushort __attribute__((const, overloadable))
    min(ushort a, ushort b) {
    return (a < b ? a : b);
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 20)
static inline int __attribute__((const, overloadable))
    min(int a, int b) {
    return (a < b ? a : b);
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 20)
static inline uint __attribute__((const, overloadable))
    min(uint a, uint b) {
    return (a < b ? a : b);
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 20)
static inline char2 __attribute__((const, overloadable))
    min(char2 a, char2 b) {
    char2 tmp;
    tmp.x = (a.x < b.x ? a.x : b.x);
    tmp.y = (a.y < b.y ? a.y : b.y);
    return tmp;
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 20)
static inline uchar2 __attribute__((const, overloadable))
    min(uchar2 a, uchar2 b) {
    uchar2 tmp;
    tmp.x = (a.x < b.x ? a.x : b.x);
    tmp.y = (a.y < b.y ? a.y : b.y);
    return tmp;
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 20)
static inline short2 __attribute__((const, overloadable))
    min(short2 a, short2 b) {
    short2 tmp;
    tmp.x = (a.x < b.x ? a.x : b.x);
    tmp.y = (a.y < b.y ? a.y : b.y);
    return tmp;
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 20)
static inline ushort2 __attribute__((const, overloadable))
    min(ushort2 a, ushort2 b) {
    ushort2 tmp;
    tmp.x = (a.x < b.x ? a.x : b.x);
    tmp.y = (a.y < b.y ? a.y : b.y);
    return tmp;
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 20)
static inline int2 __attribute__((const, overloadable))
    min(int2 a, int2 b) {
    int2 tmp;
    tmp.x = (a.x < b.x ? a.x : b.x);
    tmp.y = (a.y < b.y ? a.y : b.y);
    return tmp;
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 20)
static inline uint2 __attribute__((const, overloadable))
    min(uint2 a, uint2 b) {
    uint2 tmp;
    tmp.x = (a.x < b.x ? a.x : b.x);
    tmp.y = (a.y < b.y ? a.y : b.y);
    return tmp;
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 20)
static inline char3 __attribute__((const, overloadable))
    min(char3 a, char3 b) {
    char3 tmp;
    tmp.x = (a.x < b.x ? a.x : b.x);
    tmp.y = (a.y < b.y ? a.y : b.y);
    tmp.z = (a.z < b.z ? a.z : b.z);
    return tmp;
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 20)
static inline uchar3 __attribute__((const, overloadable))
    min(uchar3 a, uchar3 b) {
    uchar3 tmp;
    tmp.x = (a.x < b.x ? a.x : b.x);
    tmp.y = (a.y < b.y ? a.y : b.y);
    tmp.z = (a.z < b.z ? a.z : b.z);
    return tmp;
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 20)
static inline short3 __attribute__((const, overloadable))
    min(short3 a, short3 b) {
    short3 tmp;
    tmp.x = (a.x < b.x ? a.x : b.x);
    tmp.y = (a.y < b.y ? a.y : b.y);
    tmp.z = (a.z < b.z ? a.z : b.z);
    return tmp;
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 20)
static inline ushort3 __attribute__((const, overloadable))
    min(ushort3 a, ushort3 b) {
    ushort3 tmp;
    tmp.x = (a.x < b.x ? a.x : b.x);
    tmp.y = (a.y < b.y ? a.y : b.y);
    tmp.z = (a.z < b.z ? a.z : b.z);
    return tmp;
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 20)
static inline int3 __attribute__((const, overloadable))
    min(int3 a, int3 b) {
    int3 tmp;
    tmp.x = (a.x < b.x ? a.x : b.x);
    tmp.y = (a.y < b.y ? a.y : b.y);
    tmp.z = (a.z < b.z ? a.z : b.z);
    return tmp;
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 20)
static inline uint3 __attribute__((const, overloadable))
    min(uint3 a, uint3 b) {
    uint3 tmp;
    tmp.x = (a.x < b.x ? a.x : b.x);
    tmp.y = (a.y < b.y ? a.y : b.y);
    tmp.z = (a.z < b.z ? a.z : b.z);
    return tmp;
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 20)
static inline char4 __attribute__((const, overloadable))
    min(char4 a, char4 b) {
    char4 tmp;
    tmp.x = (a.x < b.x ? a.x : b.x);
    tmp.y = (a.y < b.y ? a.y : b.y);
    tmp.z = (a.z < b.z ? a.z : b.z);
    tmp.w = (a.w < b.w ? a.w : b.w);
    return tmp;
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 20)
static inline uchar4 __attribute__((const, overloadable))
    min(uchar4 a, uchar4 b) {
    uchar4 tmp;
    tmp.x = (a.x < b.x ? a.x : b.x);
    tmp.y = (a.y < b.y ? a.y : b.y);
    tmp.z = (a.z < b.z ? a.z : b.z);
    tmp.w = (a.w < b.w ? a.w : b.w);
    return tmp;
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 20)
static inline short4 __attribute__((const, overloadable))
    min(short4 a, short4 b) {
    short4 tmp;
    tmp.x = (a.x < b.x ? a.x : b.x);
    tmp.y = (a.y < b.y ? a.y : b.y);
    tmp.z = (a.z < b.z ? a.z : b.z);
    tmp.w = (a.w < b.w ? a.w : b.w);
    return tmp;
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 20)
static inline ushort4 __attribute__((const, overloadable))
    min(ushort4 a, ushort4 b) {
    ushort4 tmp;
    tmp.x = (a.x < b.x ? a.x : b.x);
    tmp.y = (a.y < b.y ? a.y : b.y);
    tmp.z = (a.z < b.z ? a.z : b.z);
    tmp.w = (a.w < b.w ? a.w : b.w);
    return tmp;
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 20)
static inline int4 __attribute__((const, overloadable))
    min(int4 a, int4 b) {
    int4 tmp;
    tmp.x = (a.x < b.x ? a.x : b.x);
    tmp.y = (a.y < b.y ? a.y : b.y);
    tmp.z = (a.z < b.z ? a.z : b.z);
    tmp.w = (a.w < b.w ? a.w : b.w);
    return tmp;
}
#endif

#if !defined(RS_VERSION) || (RS_VERSION <= 20)
static inline uint4 __attribute__((const, overloadable))
    min(uint4 a, uint4 b) {
    uint4 tmp;
    tmp.x = (a.x < b.x ? a.x : b.x);
    tmp.y = (a.y < b.y ? a.y : b.y);
    tmp.z = (a.z < b.z ? a.z : b.z);
    tmp.w = (a.w < b.w ? a.w : b.w);
    return tmp;
}
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern char __attribute__((const, overloadable))
    min(char a, char b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern char2 __attribute__((const, overloadable))
    min(char2 a, char2 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern char3 __attribute__((const, overloadable))
    min(char3 a, char3 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern char4 __attribute__((const, overloadable))
    min(char4 a, char4 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern uchar __attribute__((const, overloadable))
    min(uchar a, uchar b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern uchar2 __attribute__((const, overloadable))
    min(uchar2 a, uchar2 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern uchar3 __attribute__((const, overloadable))
    min(uchar3 a, uchar3 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern uchar4 __attribute__((const, overloadable))
    min(uchar4 a, uchar4 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern short __attribute__((const, overloadable))
    min(short a, short b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern short2 __attribute__((const, overloadable))
    min(short2 a, short2 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern short3 __attribute__((const, overloadable))
    min(short3 a, short3 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern short4 __attribute__((const, overloadable))
    min(short4 a, short4 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern ushort __attribute__((const, overloadable))
    min(ushort a, ushort b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern ushort2 __attribute__((const, overloadable))
    min(ushort2 a, ushort2 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern ushort3 __attribute__((const, overloadable))
    min(ushort3 a, ushort3 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern ushort4 __attribute__((const, overloadable))
    min(ushort4 a, ushort4 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern int __attribute__((const, overloadable))
    min(int a, int b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern int2 __attribute__((const, overloadable))
    min(int2 a, int2 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern int3 __attribute__((const, overloadable))
    min(int3 a, int3 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern int4 __attribute__((const, overloadable))
    min(int4 a, int4 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern uint __attribute__((const, overloadable))
    min(uint a, uint b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern uint2 __attribute__((const, overloadable))
    min(uint2 a, uint2 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern uint3 __attribute__((const, overloadable))
    min(uint3 a, uint3 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern uint4 __attribute__((const, overloadable))
    min(uint4 a, uint4 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern long __attribute__((const, overloadable))
    min(long a, long b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern long2 __attribute__((const, overloadable))
    min(long2 a, long2 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern long3 __attribute__((const, overloadable))
    min(long3 a, long3 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern long4 __attribute__((const, overloadable))
    min(long4 a, long4 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern ulong __attribute__((const, overloadable))
    min(ulong a, ulong b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern ulong2 __attribute__((const, overloadable))
    min(ulong2 a, ulong2 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern ulong3 __attribute__((const, overloadable))
    min(ulong3 a, ulong3 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern ulong4 __attribute__((const, overloadable))
    min(ulong4 a, ulong4 b);
#endif

/*
 * mix: Mixes two values
 *
 * Returns start + ((stop - start) * fraction).
 *
 * This can be useful for mixing two values.  For example, to create a new color that is
 * 40% color1 and 60% color2, use mix(color1, color2, 0.6f).
 */
extern float __attribute__((const, overloadable))
    mix(float start, float stop, float fraction);

extern float2 __attribute__((const, overloadable))
    mix(float2 start, float2 stop, float2 fraction);

extern float3 __attribute__((const, overloadable))
    mix(float3 start, float3 stop, float3 fraction);

extern float4 __attribute__((const, overloadable))
    mix(float4 start, float4 stop, float4 fraction);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    mix(half start, half stop, half fraction);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    mix(half2 start, half2 stop, half2 fraction);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    mix(half3 start, half3 stop, half3 fraction);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    mix(half4 start, half4 stop, half4 fraction);
#endif

extern float2 __attribute__((const, overloadable))
    mix(float2 start, float2 stop, float fraction);

extern float3 __attribute__((const, overloadable))
    mix(float3 start, float3 stop, float fraction);

extern float4 __attribute__((const, overloadable))
    mix(float4 start, float4 stop, float fraction);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    mix(half2 start, half2 stop, half fraction);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    mix(half3 start, half3 stop, half fraction);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    mix(half4 start, half4 stop, half fraction);
#endif

/*
 * modf: Integral and fractional components
 *
 * Returns the integral and fractional components of a number.
 *
 * Both components will have the same sign as x.  For example, for an input of -3.72f,
 * *integral_part will be set to -3.f and .72f will be returned.
 *
 * Parameters:
 *   v: Source value.
 *   integral_part: *integral_part will be set to the integral portion of the number.
 *
 * Returns: Floating point portion of the value.
 */
extern float __attribute__((overloadable))
    modf(float v, float* integral_part);

extern float2 __attribute__((overloadable))
    modf(float2 v, float2* integral_part);

extern float3 __attribute__((overloadable))
    modf(float3 v, float3* integral_part);

extern float4 __attribute__((overloadable))
    modf(float4 v, float4* integral_part);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((overloadable))
    modf(half v, half* integral_part);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((overloadable))
    modf(half2 v, half2* integral_part);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((overloadable))
    modf(half3 v, half3* integral_part);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((overloadable))
    modf(half4 v, half4* integral_part);
#endif

/*
 * nan: Not a Number
 *
 * Returns a NaN value (Not a Number).
 *
 * Parameters:
 *   v: Not used.
 */
extern float __attribute__((const, overloadable))
    nan(uint v);

/*
 * nan_half: Not a Number
 *
 *  Returns a half-precision floating point NaN value (Not a Number).
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    nan_half(void);
#endif

/*
 * native_acos: Approximate inverse cosine
 *
 * Returns the approximate inverse cosine, in radians.
 *
 * This function yields undefined results from input values less than -1 or greater than 1.
 *
 * See also acos().
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float __attribute__((const, overloadable))
    native_acos(float v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float2 __attribute__((const, overloadable))
    native_acos(float2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float3 __attribute__((const, overloadable))
    native_acos(float3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float4 __attribute__((const, overloadable))
    native_acos(float4 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    native_acos(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    native_acos(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    native_acos(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    native_acos(half4 v);
#endif

/*
 * native_acosh: Approximate inverse hyperbolic cosine
 *
 * Returns the approximate inverse hyperbolic cosine, in radians.
 *
 * See also acosh().
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float __attribute__((const, overloadable))
    native_acosh(float v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float2 __attribute__((const, overloadable))
    native_acosh(float2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float3 __attribute__((const, overloadable))
    native_acosh(float3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float4 __attribute__((const, overloadable))
    native_acosh(float4 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    native_acosh(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    native_acosh(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    native_acosh(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    native_acosh(half4 v);
#endif

/*
 * native_acospi: Approximate inverse cosine divided by pi
 *
 * Returns the approximate inverse cosine in radians, divided by pi.
 *
 * To get an inverse cosine measured in degrees, use acospi(a) * 180.f.
 *
 * This function yields undefined results from input values less than -1 or greater than 1.
 *
 * See also acospi().
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float __attribute__((const, overloadable))
    native_acospi(float v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float2 __attribute__((const, overloadable))
    native_acospi(float2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float3 __attribute__((const, overloadable))
    native_acospi(float3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float4 __attribute__((const, overloadable))
    native_acospi(float4 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    native_acospi(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    native_acospi(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    native_acospi(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    native_acospi(half4 v);
#endif

/*
 * native_asin: Approximate inverse sine
 *
 * Returns the approximate inverse sine, in radians.
 *
 * This function yields undefined results from input values less than -1 or greater than 1.
 *
 * See also asin().
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float __attribute__((const, overloadable))
    native_asin(float v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float2 __attribute__((const, overloadable))
    native_asin(float2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float3 __attribute__((const, overloadable))
    native_asin(float3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float4 __attribute__((const, overloadable))
    native_asin(float4 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    native_asin(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    native_asin(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    native_asin(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    native_asin(half4 v);
#endif

/*
 * native_asinh: Approximate inverse hyperbolic sine
 *
 * Returns the approximate inverse hyperbolic sine, in radians.
 *
 * See also asinh().
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float __attribute__((const, overloadable))
    native_asinh(float v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float2 __attribute__((const, overloadable))
    native_asinh(float2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float3 __attribute__((const, overloadable))
    native_asinh(float3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float4 __attribute__((const, overloadable))
    native_asinh(float4 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    native_asinh(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    native_asinh(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    native_asinh(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    native_asinh(half4 v);
#endif

/*
 * native_asinpi: Approximate inverse sine divided by pi
 *
 * Returns the approximate inverse sine in radians, divided by pi.
 *
 * To get an inverse sine measured in degrees, use asinpi(a) * 180.f.
 *
 * This function yields undefined results from input values less than -1 or greater than 1.
 *
 * See also asinpi().
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float __attribute__((const, overloadable))
    native_asinpi(float v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float2 __attribute__((const, overloadable))
    native_asinpi(float2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float3 __attribute__((const, overloadable))
    native_asinpi(float3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float4 __attribute__((const, overloadable))
    native_asinpi(float4 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    native_asinpi(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    native_asinpi(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    native_asinpi(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    native_asinpi(half4 v);
#endif

/*
 * native_atan: Approximate inverse tangent
 *
 * Returns the approximate inverse tangent, in radians.
 *
 * See also atan().
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float __attribute__((const, overloadable))
    native_atan(float v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float2 __attribute__((const, overloadable))
    native_atan(float2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float3 __attribute__((const, overloadable))
    native_atan(float3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float4 __attribute__((const, overloadable))
    native_atan(float4 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    native_atan(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    native_atan(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    native_atan(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    native_atan(half4 v);
#endif

/*
 * native_atan2: Approximate inverse tangent of a ratio
 *
 * Returns the approximate inverse tangent of (numerator / denominator), in radians.
 *
 * See also atan2().
 *
 * Parameters:
 *   numerator: Numerator.
 *   denominator: Denominator.  Can be 0.
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float __attribute__((const, overloadable))
    native_atan2(float numerator, float denominator);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float2 __attribute__((const, overloadable))
    native_atan2(float2 numerator, float2 denominator);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float3 __attribute__((const, overloadable))
    native_atan2(float3 numerator, float3 denominator);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float4 __attribute__((const, overloadable))
    native_atan2(float4 numerator, float4 denominator);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    native_atan2(half numerator, half denominator);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    native_atan2(half2 numerator, half2 denominator);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    native_atan2(half3 numerator, half3 denominator);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    native_atan2(half4 numerator, half4 denominator);
#endif

/*
 * native_atan2pi: Approximate inverse tangent of a ratio, divided by pi
 *
 * Returns the approximate inverse tangent of (numerator / denominator),
 * in radians, divided by pi.
 *
 * To get an inverse tangent measured in degrees, use atan2pi(n, d) * 180.f.
 *
 * See also atan2pi().
 *
 * Parameters:
 *   numerator: Numerator.
 *   denominator: Denominator.  Can be 0.
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float __attribute__((const, overloadable))
    native_atan2pi(float numerator, float denominator);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float2 __attribute__((const, overloadable))
    native_atan2pi(float2 numerator, float2 denominator);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float3 __attribute__((const, overloadable))
    native_atan2pi(float3 numerator, float3 denominator);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float4 __attribute__((const, overloadable))
    native_atan2pi(float4 numerator, float4 denominator);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    native_atan2pi(half numerator, half denominator);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    native_atan2pi(half2 numerator, half2 denominator);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    native_atan2pi(half3 numerator, half3 denominator);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    native_atan2pi(half4 numerator, half4 denominator);
#endif

/*
 * native_atanh: Approximate inverse hyperbolic tangent
 *
 * Returns the approximate inverse hyperbolic tangent, in radians.
 *
 * See also atanh().
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float __attribute__((const, overloadable))
    native_atanh(float v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float2 __attribute__((const, overloadable))
    native_atanh(float2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float3 __attribute__((const, overloadable))
    native_atanh(float3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float4 __attribute__((const, overloadable))
    native_atanh(float4 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    native_atanh(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    native_atanh(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    native_atanh(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    native_atanh(half4 v);
#endif

/*
 * native_atanpi: Approximate inverse tangent divided by pi
 *
 * Returns the approximate inverse tangent in radians, divided by pi.
 *
 * To get an inverse tangent measured in degrees, use atanpi(a) * 180.f.
 *
 * See also atanpi().
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float __attribute__((const, overloadable))
    native_atanpi(float v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float2 __attribute__((const, overloadable))
    native_atanpi(float2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float3 __attribute__((const, overloadable))
    native_atanpi(float3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float4 __attribute__((const, overloadable))
    native_atanpi(float4 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    native_atanpi(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    native_atanpi(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    native_atanpi(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    native_atanpi(half4 v);
#endif

/*
 * native_cbrt: Approximate cube root
 *
 * Returns the approximate cubic root.
 *
 * See also cbrt().
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float __attribute__((const, overloadable))
    native_cbrt(float v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float2 __attribute__((const, overloadable))
    native_cbrt(float2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float3 __attribute__((const, overloadable))
    native_cbrt(float3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float4 __attribute__((const, overloadable))
    native_cbrt(float4 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    native_cbrt(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    native_cbrt(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    native_cbrt(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    native_cbrt(half4 v);
#endif

/*
 * native_cos: Approximate cosine
 *
 * Returns the approximate cosine of an angle measured in radians.
 *
 * See also cos().
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float __attribute__((const, overloadable))
    native_cos(float v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float2 __attribute__((const, overloadable))
    native_cos(float2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float3 __attribute__((const, overloadable))
    native_cos(float3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float4 __attribute__((const, overloadable))
    native_cos(float4 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    native_cos(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    native_cos(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    native_cos(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    native_cos(half4 v);
#endif

/*
 * native_cosh: Approximate hypebolic cosine
 *
 * Returns the approximate hypebolic cosine.
 *
 * See also cosh().
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float __attribute__((const, overloadable))
    native_cosh(float v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float2 __attribute__((const, overloadable))
    native_cosh(float2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float3 __attribute__((const, overloadable))
    native_cosh(float3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float4 __attribute__((const, overloadable))
    native_cosh(float4 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    native_cosh(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    native_cosh(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    native_cosh(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    native_cosh(half4 v);
#endif

/*
 * native_cospi: Approximate cosine of a number multiplied by pi
 *
 * Returns the approximate cosine of (v * pi), where (v * pi) is measured in radians.
 *
 * To get the cosine of a value measured in degrees, call cospi(v / 180.f).
 *
 * See also cospi().
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float __attribute__((const, overloadable))
    native_cospi(float v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float2 __attribute__((const, overloadable))
    native_cospi(float2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float3 __attribute__((const, overloadable))
    native_cospi(float3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float4 __attribute__((const, overloadable))
    native_cospi(float4 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    native_cospi(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    native_cospi(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    native_cospi(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    native_cospi(half4 v);
#endif

/*
 * native_divide: Approximate division
 *
 * Computes the approximate division of two values.
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float __attribute__((const, overloadable))
    native_divide(float left_vector, float right_vector);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float2 __attribute__((const, overloadable))
    native_divide(float2 left_vector, float2 right_vector);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float3 __attribute__((const, overloadable))
    native_divide(float3 left_vector, float3 right_vector);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float4 __attribute__((const, overloadable))
    native_divide(float4 left_vector, float4 right_vector);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    native_divide(half left_vector, half right_vector);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    native_divide(half2 left_vector, half2 right_vector);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    native_divide(half3 left_vector, half3 right_vector);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    native_divide(half4 left_vector, half4 right_vector);
#endif

/*
 * native_exp: Approximate e raised to a number
 *
 * Fast approximate exp.
 *
 * It is valid for inputs from -86.f to 86.f.  The precision is no worse than what would be
 * expected from using 16 bit floating point values.
 *
 * See also exp().
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern float __attribute__((const, overloadable))
    native_exp(float v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern float2 __attribute__((const, overloadable))
    native_exp(float2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern float3 __attribute__((const, overloadable))
    native_exp(float3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern float4 __attribute__((const, overloadable))
    native_exp(float4 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    native_exp(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    native_exp(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    native_exp(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    native_exp(half4 v);
#endif

/*
 * native_exp10: Approximate 10 raised to a number
 *
 * Fast approximate exp10.
 *
 * It is valid for inputs from -37.f to 37.f.  The precision is no worse than what would be
 * expected from using 16 bit floating point values.
 *
 * See also exp10().
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern float __attribute__((const, overloadable))
    native_exp10(float v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern float2 __attribute__((const, overloadable))
    native_exp10(float2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern float3 __attribute__((const, overloadable))
    native_exp10(float3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern float4 __attribute__((const, overloadable))
    native_exp10(float4 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    native_exp10(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    native_exp10(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    native_exp10(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    native_exp10(half4 v);
#endif

/*
 * native_exp2: Approximate 2 raised to a number
 *
 * Fast approximate exp2.
 *
 * It is valid for inputs from -125.f to 125.f.  The precision is no worse than what would be
 * expected from using 16 bit floating point values.
 *
 * See also exp2().
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern float __attribute__((const, overloadable))
    native_exp2(float v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern float2 __attribute__((const, overloadable))
    native_exp2(float2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern float3 __attribute__((const, overloadable))
    native_exp2(float3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern float4 __attribute__((const, overloadable))
    native_exp2(float4 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    native_exp2(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    native_exp2(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    native_exp2(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    native_exp2(half4 v);
#endif

/*
 * native_expm1: Approximate e raised to a number minus one
 *
 * Returns the approximate (e ^ v) - 1.
 *
 * See also expm1().
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float __attribute__((const, overloadable))
    native_expm1(float v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float2 __attribute__((const, overloadable))
    native_expm1(float2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float3 __attribute__((const, overloadable))
    native_expm1(float3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float4 __attribute__((const, overloadable))
    native_expm1(float4 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    native_expm1(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    native_expm1(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    native_expm1(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    native_expm1(half4 v);
#endif

/*
 * native_hypot: Approximate hypotenuse
 *
 * Returns the approximate native_sqrt(a * a + b * b)
 *
 * See also hypot().
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float __attribute__((const, overloadable))
    native_hypot(float a, float b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float2 __attribute__((const, overloadable))
    native_hypot(float2 a, float2 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float3 __attribute__((const, overloadable))
    native_hypot(float3 a, float3 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float4 __attribute__((const, overloadable))
    native_hypot(float4 a, float4 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    native_hypot(half a, half b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    native_hypot(half2 a, half2 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    native_hypot(half3 a, half3 b);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    native_hypot(half4 a, half4 b);
#endif

/*
 * native_log: Approximate natural logarithm
 *
 * Fast approximate log.
 *
 * It is not accurate for values very close to zero.
 *
 * See also log().
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern float __attribute__((const, overloadable))
    native_log(float v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern float2 __attribute__((const, overloadable))
    native_log(float2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern float3 __attribute__((const, overloadable))
    native_log(float3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern float4 __attribute__((const, overloadable))
    native_log(float4 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    native_log(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    native_log(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    native_log(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    native_log(half4 v);
#endif

/*
 * native_log10: Approximate base 10 logarithm
 *
 * Fast approximate log10.
 *
 * It is not accurate for values very close to zero.
 *
 * See also log10().
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern float __attribute__((const, overloadable))
    native_log10(float v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern float2 __attribute__((const, overloadable))
    native_log10(float2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern float3 __attribute__((const, overloadable))
    native_log10(float3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern float4 __attribute__((const, overloadable))
    native_log10(float4 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    native_log10(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    native_log10(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    native_log10(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    native_log10(half4 v);
#endif

/*
 * native_log1p: Approximate natural logarithm of a value plus 1
 *
 * Returns the approximate natural logarithm of (v + 1.0f)
 *
 * See also log1p().
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float __attribute__((const, overloadable))
    native_log1p(float v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float2 __attribute__((const, overloadable))
    native_log1p(float2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float3 __attribute__((const, overloadable))
    native_log1p(float3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float4 __attribute__((const, overloadable))
    native_log1p(float4 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    native_log1p(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    native_log1p(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    native_log1p(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    native_log1p(half4 v);
#endif

/*
 * native_log2: Approximate base 2 logarithm
 *
 * Fast approximate log2.
 *
 * It is not accurate for values very close to zero.
 *
 * See also log2().
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern float __attribute__((const, overloadable))
    native_log2(float v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern float2 __attribute__((const, overloadable))
    native_log2(float2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern float3 __attribute__((const, overloadable))
    native_log2(float3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern float4 __attribute__((const, overloadable))
    native_log2(float4 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    native_log2(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    native_log2(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    native_log2(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    native_log2(half4 v);
#endif

/*
 * native_powr: Approximate positive base raised to an exponent
 *
 * Fast approximate (base ^ exponent).
 *
 * See also powr().
 *
 * Parameters:
 *   base: Must be between 0.f and 256.f.  The function is not accurate for values very close to zero.
 *   exponent: Must be between -15.f and 15.f.
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern float __attribute__((const, overloadable))
    native_powr(float base, float exponent);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern float2 __attribute__((const, overloadable))
    native_powr(float2 base, float2 exponent);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern float3 __attribute__((const, overloadable))
    native_powr(float3 base, float3 exponent);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 18))
extern float4 __attribute__((const, overloadable))
    native_powr(float4 base, float4 exponent);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    native_powr(half base, half exponent);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    native_powr(half2 base, half2 exponent);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    native_powr(half3 base, half3 exponent);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    native_powr(half4 base, half4 exponent);
#endif

/*
 * native_recip: Approximate reciprocal
 *
 * Returns the approximate approximate reciprocal of a value.
 *
 * See also half_recip().
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float __attribute__((const, overloadable))
    native_recip(float v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float2 __attribute__((const, overloadable))
    native_recip(float2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float3 __attribute__((const, overloadable))
    native_recip(float3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float4 __attribute__((const, overloadable))
    native_recip(float4 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    native_recip(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    native_recip(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    native_recip(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    native_recip(half4 v);
#endif

/*
 * native_rootn: Approximate nth root
 *
 * Compute the approximate Nth root of a value.
 *
 * See also rootn().
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float __attribute__((const, overloadable))
    native_rootn(float v, int n);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float2 __attribute__((const, overloadable))
    native_rootn(float2 v, int2 n);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float3 __attribute__((const, overloadable))
    native_rootn(float3 v, int3 n);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float4 __attribute__((const, overloadable))
    native_rootn(float4 v, int4 n);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    native_rootn(half v, int n);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    native_rootn(half2 v, int2 n);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    native_rootn(half3 v, int3 n);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    native_rootn(half4 v, int4 n);
#endif

/*
 * native_rsqrt: Approximate reciprocal of a square root
 *
 * Returns approximate (1 / sqrt(v)).
 *
 * See also rsqrt(), half_rsqrt().
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float __attribute__((const, overloadable))
    native_rsqrt(float v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float2 __attribute__((const, overloadable))
    native_rsqrt(float2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float3 __attribute__((const, overloadable))
    native_rsqrt(float3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float4 __attribute__((const, overloadable))
    native_rsqrt(float4 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    native_rsqrt(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    native_rsqrt(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    native_rsqrt(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    native_rsqrt(half4 v);
#endif

/*
 * native_sin: Approximate sine
 *
 * Returns the approximate sine of an angle measured in radians.
 *
 * See also sin().
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float __attribute__((const, overloadable))
    native_sin(float v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float2 __attribute__((const, overloadable))
    native_sin(float2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float3 __attribute__((const, overloadable))
    native_sin(float3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float4 __attribute__((const, overloadable))
    native_sin(float4 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    native_sin(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    native_sin(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    native_sin(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    native_sin(half4 v);
#endif

/*
 * native_sincos: Approximate sine and cosine
 *
 * Returns the approximate sine and cosine of a value.
 *
 * See also sincos().
 *
 * Parameters:
 *   v: Incoming value in radians.
 *   cos: *cos will be set to the cosine value.
 *
 * Returns: Sine.
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float __attribute__((overloadable))
    native_sincos(float v, float* cos);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float2 __attribute__((overloadable))
    native_sincos(float2 v, float2* cos);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float3 __attribute__((overloadable))
    native_sincos(float3 v, float3* cos);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float4 __attribute__((overloadable))
    native_sincos(float4 v, float4* cos);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((overloadable))
    native_sincos(half v, half* cos);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((overloadable))
    native_sincos(half2 v, half2* cos);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((overloadable))
    native_sincos(half3 v, half3* cos);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((overloadable))
    native_sincos(half4 v, half4* cos);
#endif

/*
 * native_sinh: Approximate hyperbolic sine
 *
 * Returns the approximate hyperbolic sine of a value specified in radians.
 *
 * See also sinh().
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float __attribute__((const, overloadable))
    native_sinh(float v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float2 __attribute__((const, overloadable))
    native_sinh(float2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float3 __attribute__((const, overloadable))
    native_sinh(float3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float4 __attribute__((const, overloadable))
    native_sinh(float4 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    native_sinh(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    native_sinh(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    native_sinh(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    native_sinh(half4 v);
#endif

/*
 * native_sinpi: Approximate sine of a number multiplied by pi
 *
 * Returns the approximate sine of (v * pi), where (v * pi) is measured in radians.
 *
 * To get the sine of a value measured in degrees, call sinpi(v / 180.f).
 *
 * See also sinpi().
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float __attribute__((const, overloadable))
    native_sinpi(float v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float2 __attribute__((const, overloadable))
    native_sinpi(float2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float3 __attribute__((const, overloadable))
    native_sinpi(float3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float4 __attribute__((const, overloadable))
    native_sinpi(float4 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    native_sinpi(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    native_sinpi(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    native_sinpi(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    native_sinpi(half4 v);
#endif

/*
 * native_sqrt: Approximate square root
 *
 * Returns the approximate sqrt(v).
 *
 * See also sqrt(), half_sqrt().
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float __attribute__((const, overloadable))
    native_sqrt(float v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float2 __attribute__((const, overloadable))
    native_sqrt(float2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float3 __attribute__((const, overloadable))
    native_sqrt(float3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float4 __attribute__((const, overloadable))
    native_sqrt(float4 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    native_sqrt(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    native_sqrt(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    native_sqrt(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    native_sqrt(half4 v);
#endif

/*
 * native_tan: Approximate tangent
 *
 * Returns the approximate tangent of an angle measured in radians.
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float __attribute__((const, overloadable))
    native_tan(float v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float2 __attribute__((const, overloadable))
    native_tan(float2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float3 __attribute__((const, overloadable))
    native_tan(float3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float4 __attribute__((const, overloadable))
    native_tan(float4 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    native_tan(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    native_tan(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    native_tan(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    native_tan(half4 v);
#endif

/*
 * native_tanh: Approximate hyperbolic tangent
 *
 * Returns the approximate hyperbolic tangent of a value.
 *
 * See also tanh().
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float __attribute__((const, overloadable))
    native_tanh(float v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float2 __attribute__((const, overloadable))
    native_tanh(float2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float3 __attribute__((const, overloadable))
    native_tanh(float3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float4 __attribute__((const, overloadable))
    native_tanh(float4 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    native_tanh(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    native_tanh(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    native_tanh(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    native_tanh(half4 v);
#endif

/*
 * native_tanpi: Approximate tangent of a number multiplied by pi
 *
 * Returns the approximate tangent of (v * pi), where (v * pi) is measured in radians.
 *
 * To get the tangent of a value measured in degrees, call tanpi(v / 180.f).
 *
 * See also tanpi().
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float __attribute__((const, overloadable))
    native_tanpi(float v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float2 __attribute__((const, overloadable))
    native_tanpi(float2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float3 __attribute__((const, overloadable))
    native_tanpi(float3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float4 __attribute__((const, overloadable))
    native_tanpi(float4 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    native_tanpi(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    native_tanpi(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    native_tanpi(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    native_tanpi(half4 v);
#endif

/*
 * nextafter: Next floating point number
 *
 * Returns the next representable floating point number from v towards target.
 *
 * In rs_fp_relaxed mode, a denormalized input value may not yield the next denormalized
 * value, as support of denormalized values is optional in relaxed mode.
 */
extern float __attribute__((const, overloadable))
    nextafter(float v, float target);

extern float2 __attribute__((const, overloadable))
    nextafter(float2 v, float2 target);

extern float3 __attribute__((const, overloadable))
    nextafter(float3 v, float3 target);

extern float4 __attribute__((const, overloadable))
    nextafter(float4 v, float4 target);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    nextafter(half v, half target);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    nextafter(half2 v, half2 target);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    nextafter(half3 v, half3 target);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    nextafter(half4 v, half4 target);
#endif

/*
 * pow: Base raised to an exponent
 *
 * Returns base raised to the power exponent, i.e. base ^ exponent.
 *
 * pown() and powr() are similar.  pown() takes an integer exponent. powr() assumes the
 * base to be non-negative.
 */
extern float __attribute__((const, overloadable))
    pow(float base, float exponent);

extern float2 __attribute__((const, overloadable))
    pow(float2 base, float2 exponent);

extern float3 __attribute__((const, overloadable))
    pow(float3 base, float3 exponent);

extern float4 __attribute__((const, overloadable))
    pow(float4 base, float4 exponent);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    pow(half base, half exponent);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    pow(half2 base, half2 exponent);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    pow(half3 base, half3 exponent);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    pow(half4 base, half4 exponent);
#endif

/*
 * pown: Base raised to an integer exponent
 *
 * Returns base raised to the power exponent, i.e. base ^ exponent.
 *
 * pow() and powr() are similar.  The both take a float exponent. powr() also assumes the
 * base to be non-negative.
 */
extern float __attribute__((const, overloadable))
    pown(float base, int exponent);

extern float2 __attribute__((const, overloadable))
    pown(float2 base, int2 exponent);

extern float3 __attribute__((const, overloadable))
    pown(float3 base, int3 exponent);

extern float4 __attribute__((const, overloadable))
    pown(float4 base, int4 exponent);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    pown(half base, int exponent);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    pown(half2 base, int2 exponent);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    pown(half3 base, int3 exponent);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    pown(half4 base, int4 exponent);
#endif

/*
 * powr: Positive base raised to an exponent
 *
 * Returns base raised to the power exponent, i.e. base ^ exponent.  base must be >= 0.
 *
 * pow() and pown() are similar.  They both make no assumptions about the base.
 * pow() takes a float exponent while pown() take an integer.
 *
 * See also native_powr().
 */
extern float __attribute__((const, overloadable))
    powr(float base, float exponent);

extern float2 __attribute__((const, overloadable))
    powr(float2 base, float2 exponent);

extern float3 __attribute__((const, overloadable))
    powr(float3 base, float3 exponent);

extern float4 __attribute__((const, overloadable))
    powr(float4 base, float4 exponent);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    powr(half base, half exponent);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    powr(half2 base, half2 exponent);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    powr(half3 base, half3 exponent);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    powr(half4 base, half4 exponent);
#endif

/*
 * radians: Converts degrees into radians
 *
 * Converts from degrees to radians.
 */
extern float __attribute__((const, overloadable))
    radians(float v);

extern float2 __attribute__((const, overloadable))
    radians(float2 v);

extern float3 __attribute__((const, overloadable))
    radians(float3 v);

extern float4 __attribute__((const, overloadable))
    radians(float4 v);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    radians(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    radians(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    radians(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    radians(half4 v);
#endif

/*
 * remainder: Remainder of a division
 *
 * Returns the remainder of (numerator / denominator), where the quotient is rounded towards
 * the nearest integer.
 *
 * The function fmod() is similar but rounds toward the closest interger.
 * For example, fmod(-3.8f, 2.f) returns -1.8f (-3.8f - -1.f * 2.f)
 * while remainder(-3.8f, 2.f) returns 0.2f (-3.8f - -2.f * 2.f).
 */
extern float __attribute__((const, overloadable))
    remainder(float numerator, float denominator);

extern float2 __attribute__((const, overloadable))
    remainder(float2 numerator, float2 denominator);

extern float3 __attribute__((const, overloadable))
    remainder(float3 numerator, float3 denominator);

extern float4 __attribute__((const, overloadable))
    remainder(float4 numerator, float4 denominator);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    remainder(half numerator, half denominator);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    remainder(half2 numerator, half2 denominator);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    remainder(half3 numerator, half3 denominator);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    remainder(half4 numerator, half4 denominator);
#endif

/*
 * remquo: Remainder and quotient of a division
 *
 * Returns the quotient and the remainder of (numerator / denominator).
 *
 * Only the sign and lowest three bits of the quotient are guaranteed to be accurate.
 *
 * This function is useful for implementing periodic functions.  The low three bits of the
 * quotient gives the quadrant and the remainder the distance within the quadrant.
 * For example, an implementation of sin(x) could call remquo(x, PI / 2.f, &quadrant)
 * to reduce very large value of x to something within a limited range.
 *
 * Example: remquo(-23.5f, 8.f, &quot) sets the lowest three bits of quot to 3
 * and the sign negative.  It returns 0.5f.
 *
 * Parameters:
 *   numerator: Numerator.
 *   denominator: Denominator.
 *   quotient: *quotient will be set to the integer quotient.
 *
 * Returns: Remainder, precise only for the low three bits.
 */
extern float __attribute__((overloadable))
    remquo(float numerator, float denominator, int* quotient);

extern float2 __attribute__((overloadable))
    remquo(float2 numerator, float2 denominator, int2* quotient);

extern float3 __attribute__((overloadable))
    remquo(float3 numerator, float3 denominator, int3* quotient);

extern float4 __attribute__((overloadable))
    remquo(float4 numerator, float4 denominator, int4* quotient);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((overloadable))
    remquo(half numerator, half denominator, int* quotient);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((overloadable))
    remquo(half2 numerator, half2 denominator, int2* quotient);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((overloadable))
    remquo(half3 numerator, half3 denominator, int3* quotient);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((overloadable))
    remquo(half4 numerator, half4 denominator, int4* quotient);
#endif

/*
 * rint: Round to even
 *
 * Rounds to the nearest integral value.
 *
 * rint() rounds half values to even.  For example, rint(0.5f) returns 0.f and
 * rint(1.5f) returns 2.f.  Similarly, rint(-0.5f) returns -0.f and
 * rint(-1.5f) returns -2.f.
 *
 * round() is similar but rounds away from zero.  trunc() truncates the decimal fraction.
 */
extern float __attribute__((const, overloadable))
    rint(float v);

extern float2 __attribute__((const, overloadable))
    rint(float2 v);

extern float3 __attribute__((const, overloadable))
    rint(float3 v);

extern float4 __attribute__((const, overloadable))
    rint(float4 v);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    rint(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    rint(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    rint(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    rint(half4 v);
#endif

/*
 * rootn: Nth root
 *
 * Compute the Nth root of a value.
 *
 * See also native_rootn().
 */
extern float __attribute__((const, overloadable))
    rootn(float v, int n);

extern float2 __attribute__((const, overloadable))
    rootn(float2 v, int2 n);

extern float3 __attribute__((const, overloadable))
    rootn(float3 v, int3 n);

extern float4 __attribute__((const, overloadable))
    rootn(float4 v, int4 n);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    rootn(half v, int n);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    rootn(half2 v, int2 n);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    rootn(half3 v, int3 n);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    rootn(half4 v, int4 n);
#endif

/*
 * round: Round away from zero
 *
 * Round to the nearest integral value.
 *
 * round() rounds half values away from zero.  For example, round(0.5f) returns 1.f
 * and round(1.5f) returns 2.f.  Similarly, round(-0.5f) returns -1.f
 * and round(-1.5f) returns -2.f.
 *
 * rint() is similar but rounds half values toward even.  trunc() truncates the decimal fraction.
 */
extern float __attribute__((const, overloadable))
    round(float v);

extern float2 __attribute__((const, overloadable))
    round(float2 v);

extern float3 __attribute__((const, overloadable))
    round(float3 v);

extern float4 __attribute__((const, overloadable))
    round(float4 v);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    round(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    round(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    round(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    round(half4 v);
#endif

/*
 * rsqrt: Reciprocal of a square root
 *
 * Returns (1 / sqrt(v)).
 *
 * See also half_rsqrt(), native_rsqrt().
 */
extern float __attribute__((const, overloadable))
    rsqrt(float v);

extern float2 __attribute__((const, overloadable))
    rsqrt(float2 v);

extern float3 __attribute__((const, overloadable))
    rsqrt(float3 v);

extern float4 __attribute__((const, overloadable))
    rsqrt(float4 v);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    rsqrt(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    rsqrt(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    rsqrt(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    rsqrt(half4 v);
#endif

/*
 * sign: Sign of a value
 *
 * Returns the sign of a value.
 *
 * if (v < 0) return -1.f;
 * else if (v > 0) return 1.f;
 * else return 0.f;
 */
extern float __attribute__((const, overloadable))
    sign(float v);

extern float2 __attribute__((const, overloadable))
    sign(float2 v);

extern float3 __attribute__((const, overloadable))
    sign(float3 v);

extern float4 __attribute__((const, overloadable))
    sign(float4 v);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    sign(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    sign(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    sign(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    sign(half4 v);
#endif

/*
 * sin: Sine
 *
 * Returns the sine of an angle measured in radians.
 *
 * See also native_sin().
 */
extern float __attribute__((const, overloadable))
    sin(float v);

extern float2 __attribute__((const, overloadable))
    sin(float2 v);

extern float3 __attribute__((const, overloadable))
    sin(float3 v);

extern float4 __attribute__((const, overloadable))
    sin(float4 v);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    sin(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    sin(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    sin(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    sin(half4 v);
#endif

/*
 * sincos: Sine and cosine
 *
 * Returns the sine and cosine of a value.
 *
 * See also native_sincos().
 *
 * Parameters:
 *   v: Incoming value in radians.
 *   cos: *cos will be set to the cosine value.
 *
 * Returns: Sine of v.
 */
extern float __attribute__((overloadable))
    sincos(float v, float* cos);

extern float2 __attribute__((overloadable))
    sincos(float2 v, float2* cos);

extern float3 __attribute__((overloadable))
    sincos(float3 v, float3* cos);

extern float4 __attribute__((overloadable))
    sincos(float4 v, float4* cos);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((overloadable))
    sincos(half v, half* cos);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((overloadable))
    sincos(half2 v, half2* cos);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((overloadable))
    sincos(half3 v, half3* cos);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((overloadable))
    sincos(half4 v, half4* cos);
#endif

/*
 * sinh: Hyperbolic sine
 *
 * Returns the hyperbolic sine of v, where v is measured in radians.
 *
 * See also native_sinh().
 */
extern float __attribute__((const, overloadable))
    sinh(float v);

extern float2 __attribute__((const, overloadable))
    sinh(float2 v);

extern float3 __attribute__((const, overloadable))
    sinh(float3 v);

extern float4 __attribute__((const, overloadable))
    sinh(float4 v);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    sinh(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    sinh(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    sinh(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    sinh(half4 v);
#endif

/*
 * sinpi: Sine of a number multiplied by pi
 *
 * Returns the sine of (v * pi), where (v * pi) is measured in radians.
 *
 * To get the sine of a value measured in degrees, call sinpi(v / 180.f).
 *
 * See also native_sinpi().
 */
extern float __attribute__((const, overloadable))
    sinpi(float v);

extern float2 __attribute__((const, overloadable))
    sinpi(float2 v);

extern float3 __attribute__((const, overloadable))
    sinpi(float3 v);

extern float4 __attribute__((const, overloadable))
    sinpi(float4 v);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    sinpi(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    sinpi(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    sinpi(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    sinpi(half4 v);
#endif

/*
 * sqrt: Square root
 *
 * Returns the square root of a value.
 *
 * See also half_sqrt(), native_sqrt().
 */
extern float __attribute__((const, overloadable))
    sqrt(float v);

extern float2 __attribute__((const, overloadable))
    sqrt(float2 v);

extern float3 __attribute__((const, overloadable))
    sqrt(float3 v);

extern float4 __attribute__((const, overloadable))
    sqrt(float4 v);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    sqrt(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    sqrt(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    sqrt(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    sqrt(half4 v);
#endif

/*
 * step: 0 if less than a value, 0 otherwise
 *
 * Returns 0.f if v < edge, 1.f otherwise.
 *
 * This can be useful to create conditional computations without using loops and branching
 * instructions.  For example, instead of computing (a[i] < b[i]) ? 0.f : atan2(a[i], b[i])
 * for the corresponding elements of a vector, you could instead use step(a, b) * atan2(a, b).
 */
extern float __attribute__((const, overloadable))
    step(float edge, float v);

extern float2 __attribute__((const, overloadable))
    step(float2 edge, float2 v);

extern float3 __attribute__((const, overloadable))
    step(float3 edge, float3 v);

extern float4 __attribute__((const, overloadable))
    step(float4 edge, float4 v);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    step(half edge, half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    step(half2 edge, half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    step(half3 edge, half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    step(half4 edge, half4 v);
#endif

extern float2 __attribute__((const, overloadable))
    step(float2 edge, float v);

extern float3 __attribute__((const, overloadable))
    step(float3 edge, float v);

extern float4 __attribute__((const, overloadable))
    step(float4 edge, float v);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    step(half2 edge, half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    step(half3 edge, half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    step(half4 edge, half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float2 __attribute__((const, overloadable))
    step(float edge, float2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float3 __attribute__((const, overloadable))
    step(float edge, float3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 21))
extern float4 __attribute__((const, overloadable))
    step(float edge, float4 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    step(half edge, half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    step(half edge, half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    step(half edge, half4 v);
#endif

/*
 * tan: Tangent
 *
 * Returns the tangent of an angle measured in radians.
 *
 * See also native_tan().
 */
extern float __attribute__((const, overloadable))
    tan(float v);

extern float2 __attribute__((const, overloadable))
    tan(float2 v);

extern float3 __attribute__((const, overloadable))
    tan(float3 v);

extern float4 __attribute__((const, overloadable))
    tan(float4 v);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    tan(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    tan(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    tan(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    tan(half4 v);
#endif

/*
 * tanh: Hyperbolic tangent
 *
 * Returns the hyperbolic tangent of a value.
 *
 * See also native_tanh().
 */
extern float __attribute__((const, overloadable))
    tanh(float v);

extern float2 __attribute__((const, overloadable))
    tanh(float2 v);

extern float3 __attribute__((const, overloadable))
    tanh(float3 v);

extern float4 __attribute__((const, overloadable))
    tanh(float4 v);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    tanh(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    tanh(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    tanh(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    tanh(half4 v);
#endif

/*
 * tanpi: Tangent of a number multiplied by pi
 *
 * Returns the tangent of (v * pi), where (v * pi) is measured in radians.
 *
 * To get the tangent of a value measured in degrees, call tanpi(v / 180.f).
 *
 * See also native_tanpi().
 */
extern float __attribute__((const, overloadable))
    tanpi(float v);

extern float2 __attribute__((const, overloadable))
    tanpi(float2 v);

extern float3 __attribute__((const, overloadable))
    tanpi(float3 v);

extern float4 __attribute__((const, overloadable))
    tanpi(float4 v);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    tanpi(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    tanpi(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    tanpi(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    tanpi(half4 v);
#endif

/*
 * tgamma: Gamma function
 *
 * Returns the gamma function of a value.
 *
 * See also lgamma().
 */
extern float __attribute__((const, overloadable))
    tgamma(float v);

extern float2 __attribute__((const, overloadable))
    tgamma(float2 v);

extern float3 __attribute__((const, overloadable))
    tgamma(float3 v);

extern float4 __attribute__((const, overloadable))
    tgamma(float4 v);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    tgamma(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    tgamma(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    tgamma(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    tgamma(half4 v);
#endif

/*
 * trunc: Truncates a floating point
 *
 * Rounds to integral using truncation.
 *
 * For example, trunc(1.7f) returns 1.f and trunc(-1.7f) returns -1.f.
 *
 * See rint() and round() for other rounding options.
 */
extern float __attribute__((const, overloadable))
    trunc(float v);

extern float2 __attribute__((const, overloadable))
    trunc(float2 v);

extern float3 __attribute__((const, overloadable))
    trunc(float3 v);

extern float4 __attribute__((const, overloadable))
    trunc(float4 v);

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half __attribute__((const, overloadable))
    trunc(half v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half2 __attribute__((const, overloadable))
    trunc(half2 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half3 __attribute__((const, overloadable))
    trunc(half3 v);
#endif

#if (defined(RS_VERSION) && (RS_VERSION >= 24))
extern half4 __attribute__((const, overloadable))
    trunc(half4 v);
#endif

/*
 * rsClamp: Restrain a value to a range
 *
 * DEPRECATED.  Do not use.
 *
 * Clamp a value between low and high.
 *
 * Parameters:
 *   amount: Value to clamp.
 *   low: Lower bound.
 *   high: Upper bound.
 */
extern char __attribute__((const, overloadable
#if (defined(RS_VERSION) && (RS_VERSION >= 22))
, deprecated("Use clamp() instead.")
#endif
))
    rsClamp(char amount, char low, char high);

extern uchar __attribute__((const, overloadable
#if (defined(RS_VERSION) && (RS_VERSION >= 22))
, deprecated("Use clamp() instead.")
#endif
))
    rsClamp(uchar amount, uchar low, uchar high);

extern short __attribute__((const, overloadable
#if (defined(RS_VERSION) && (RS_VERSION >= 22))
, deprecated("Use clamp() instead.")
#endif
))
    rsClamp(short amount, short low, short high);

extern ushort __attribute__((const, overloadable
#if (defined(RS_VERSION) && (RS_VERSION >= 22))
, deprecated("Use clamp() instead.")
#endif
))
    rsClamp(ushort amount, ushort low, ushort high);

extern int __attribute__((const, overloadable
#if (defined(RS_VERSION) && (RS_VERSION >= 22))
, deprecated("Use clamp() instead.")
#endif
))
    rsClamp(int amount, int low, int high);

extern uint __attribute__((const, overloadable
#if (defined(RS_VERSION) && (RS_VERSION >= 22))
, deprecated("Use clamp() instead.")
#endif
))
    rsClamp(uint amount, uint low, uint high);

/*
 * rsFrac: Returns the fractional part of a float
 *
 * DEPRECATED.  Do not use.
 *
 * Returns the fractional part of a float
 */
extern float __attribute__((const, overloadable
#if (defined(RS_VERSION) && (RS_VERSION >= 22))
, deprecated("Use fract() instead.")
#endif
))
    rsFrac(float v);

/*
 * rsRand: Pseudo-random number
 *
 * Return a random value between 0 (or min_value) and max_malue.
 */
extern int __attribute__((overloadable))
    rsRand(int max_value);

extern int __attribute__((overloadable))
    rsRand(int min_value, int max_value);

extern float __attribute__((overloadable))
    rsRand(float max_value);

extern float __attribute__((overloadable))
    rsRand(float min_value, float max_value);

#endif // RENDERSCRIPT_RS_MATH_RSH
