package com.lanya.btmonitor;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.provider.Settings;

/**
 * 小米设备优化助手
 * 专门处理小米设备的电池优化、自启动管理等设置
 */
public class XiaomiOptimizationHelper {
    
    private static final String TAG = "XiaomiOptimizationHelper";
    
    /**
     * 检测是否为小米设备
     */
    public static boolean isXiaomiDevice() {
        return "Xiaomi".equalsIgnoreCase(Build.MANUFACTURER) || 
               "Redmi".equalsIgnoreCase(Build.MANUFACTURER);
    }
    
    /**
     * 检测是否为HyperOS系统
     */
    public static boolean isHyperOS() {
        try {
            String version = Build.VERSION.INCREMENTAL;
            return version != null && (version.contains("HyperOS") || version.contains("HYPEROS"));
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 检测是否为Android 15
     */
    public static boolean isAndroid15() {
        return Build.VERSION.SDK_INT >= 35; // Android 15 = API 35
    }
    
    /**
     * 获取设备详细信息
     */
    public static String getDeviceInfo() {
        return String.format("设备: %s %s, 系统: Android %s (API %d), 版本: %s", 
            Build.MANUFACTURER, Build.MODEL, Build.VERSION.RELEASE, 
            Build.VERSION.SDK_INT, Build.VERSION.INCREMENTAL);
    }
    
    /**
     * 打印小米设备优化指南
     */
    public static void printOptimizationGuide() {
        if (!isXiaomiDevice()) {
            LogUtils.log(TAG, "非小米设备，跳过小米优化指南");
            return;
        }
        
        LogUtils.log(TAG, "📱 小米设备优化指南 - " + getDeviceInfo());
        LogUtils.log(TAG, "");
        LogUtils.log(TAG, "🔋 电池优化设置：");
        LogUtils.log(TAG, "   1. 设置 → 应用设置 → 应用管理 → BluetoothMonitor");
        LogUtils.log(TAG, "   2. 省电策略 → 无限制");
        LogUtils.log(TAG, "   3. 电池优化 → 不优化");
        LogUtils.log(TAG, "");
        LogUtils.log(TAG, "🚀 自启动管理：");
        LogUtils.log(TAG, "   1. 设置 → 应用设置 → 应用管理 → BluetoothMonitor");
        LogUtils.log(TAG, "   2. 自启动 → 开启");
        LogUtils.log(TAG, "   3. 后台弹出界面 → 开启");
        LogUtils.log(TAG, "");
        LogUtils.log(TAG, "🔒 锁屏显示：");
        LogUtils.log(TAG, "   1. 设置 → 应用设置 → 应用管理 → BluetoothMonitor");
        LogUtils.log(TAG, "   2. 锁屏显示 → 开启");
        LogUtils.log(TAG, "");
        
        if (isHyperOS()) {
            LogUtils.log(TAG, "🌟 HyperOS 特殊设置：");
            LogUtils.log(TAG, "   1. 设置 → 智能场景 → 应用冻结");
            LogUtils.log(TAG, "   2. 将 BluetoothMonitor 添加到白名单");
            LogUtils.log(TAG, "   3. 设置 → 隐私保护 → 特殊权限 → 设备管理器");
            LogUtils.log(TAG, "   4. 允许 BluetoothMonitor 作为设备管理器");
            LogUtils.log(TAG, "");
        }
        
        if (isAndroid15()) {
            LogUtils.log(TAG, "🆕 Android 15 特殊设置：");
            LogUtils.log(TAG, "   1. 设置 → 通知 → 应用通知 → BluetoothMonitor → 允许通知");
            LogUtils.log(TAG, "   2. 设置 → 应用 → 特殊应用访问权限 → 电池优化 → BluetoothMonitor → 不优化");
            LogUtils.log(TAG, "   3. 设置 → 位置信息 → 应用级权限 → BluetoothMonitor → 始终允许");
            LogUtils.log(TAG, "");
        }
        
        LogUtils.log(TAG, "⚠️ 重要提醒：");
        LogUtils.log(TAG, "   - 每次系统更新后需要重新检查这些设置");
        LogUtils.log(TAG, "   - 如果蓝牙扫描不稳定，请优先检查电池优化设置");
        LogUtils.log(TAG, "   - 建议将手机充电时测试，确保设置生效");
    }
    
    /**
     * 尝试打开小米设备的特殊设置页面
     */
    public static void openXiaomiSettings(Context context) {
        if (!isXiaomiDevice() || context == null) {
            return;
        }
        
        try {
            String packageName = context.getPackageName();
            
            // 尝试打开小米的应用详情页面
            Intent intent = new Intent();
            intent.setAction("miui.intent.action.APP_PERM_EDITOR");
            intent.putExtra("extra_pkgname", packageName);
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            
            context.startActivity(intent);
            LogUtils.log(TAG, "已打开小米应用权限设置页面");
            
        } catch (Exception e) {
            LogUtils.log(TAG, "无法打开小米设置页面，请手动设置: " + e.getMessage());
            
            // 备用方案：打开标准应用设置页面
            try {
                Intent intent = new Intent();
                intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                intent.setData(Uri.parse("package:" + context.getPackageName()));
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                context.startActivity(intent);
                LogUtils.log(TAG, "已打开标准应用设置页面");
            } catch (Exception e2) {
                LogUtils.logError(TAG, "无法打开任何设置页面", e2);
            }
        }
    }
    
    /**
     * 获取小米设备推荐的扫描间隔
     */
    public static long getRecommendedScanInterval(boolean isScreenOn, boolean isInWhitelist) {
        if (!isXiaomiDevice()) {
            return isInWhitelist ? (isScreenOn ? 800 : 400) : (isScreenOn ? 1500 : 1000);
        }
        
        boolean isAndroid15 = isAndroid15();
        
        if (isAndroid15) {
            // 小米 + Android 15：更保守的策略
            return isInWhitelist ? (isScreenOn ? 600 : 300) : (isScreenOn ? 2000 : 1500);
        } else {
            // 小米 + 其他Android版本
            return isInWhitelist ? (isScreenOn ? 700 : 350) : (isScreenOn ? 1800 : 1200);
        }
    }
}
