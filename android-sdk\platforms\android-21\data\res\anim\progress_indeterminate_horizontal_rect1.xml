<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2014 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<set xmlns:android="http://schemas.android.com/apk/res/android" >
    <objectAnimator
        android:duration="2000"
        android:propertyXName="translateX"
        android:pathData="M -522.59998,0 c 48.89972,0 166.02656,0 301.21729,0 c 197.58128,0 420.9827,0 420.9827,0 "
        android:interpolator="@interpolator/progress_indeterminate_horizontal_rect1_translatex"
        android:repeatCount="infinite" />
    <objectAnimator
        android:duration="2000"
        android:propertyYName="scaleX"
        android:pathData="M 0 0.1 L 1 0.826849212646 L 2 0.1"
        android:interpolator="@interpolator/progress_indeterminate_horizontal_rect1_scalex"
        android:repeatCount="infinite" />
</set>
