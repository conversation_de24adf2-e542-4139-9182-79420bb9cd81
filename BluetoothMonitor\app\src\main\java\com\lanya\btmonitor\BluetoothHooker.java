package com.lanya.btmonitor;

import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothManager;
import android.bluetooth.le.BluetoothLeScanner;
import android.bluetooth.le.ScanCallback;
import android.bluetooth.le.ScanResult;
import android.bluetooth.le.ScanSettings;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Handler;
import android.os.Looper;

import java.util.List;
import java.util.Set;

import de.robv.android.xposed.XC_MethodHook;
import de.robv.android.xposed.XposedBridge;
import de.robv.android.xposed.XposedHelpers;

/**
 * 蓝牙Hook实现类
 * 负责Hook蓝牙相关API，监控附近设备
 */
public class BluetoothHooker {

    private static final String TAG = "BluetoothHooker";

    // 目标设备信息过滤
    private static final String TARGET_DEVICE_NAME = "野猪亨利";
    private static final String TARGET_DEVICE_MAC = "41:6C:C4:CA:43:35"; // 保留用于参考

    // 简单去重控制
    private static String lastLoggedDevice = "";
    private static long lastLogTime = 0;
    private static final long LOG_INTERVAL = 1200; // 1.2秒内同一设备不重复记录

    // 简单高效的信号区间触发系统 v5.0
    // 核心逻辑：进入-80dBm范围执行解锁，离开范围执行上锁
    private static final int SIGNAL_THRESHOLD = -80; // 信号强度阈值：-80dBm

    // 状态管理
    private static boolean isInRange = false; // 当前是否在信号范围内
    private static boolean hasExecutedUnlock = false; // 当前范围内是否已执行解锁
    private static boolean hasExecutedLock = false; // 当前范围外是否已执行上锁
    private static long lastSignalTime = 0; // 最后一次收到信号的时间
    private static int lastRSSI = -999; // 最后一次的RSSI值

    // 防重复启动控制
    private static boolean isPeriodicScanningStarted = false;

    private static Context systemContext;
    private static BluetoothAdapter bluetoothAdapter;
    private static Handler mainHandler;
    private static BroadcastReceiver bluetoothReceiver;
    private static ScanCallback bleScanCallback;

    // 电源管理
    private static android.os.PowerManager.WakeLock wakeLock;
    private static android.os.PowerManager.WakeLock networkWakeLock;
    private static BroadcastReceiver screenStateReceiver;

    /**
     * 检查是否为目标设备（只根据设备名称）
     */
    private static boolean isTargetDevice(String deviceName, String deviceAddress) {
        if (deviceName == null) {
            return false;
        }
        // 只匹配设备名称，MAC地址会变动
        return TARGET_DEVICE_NAME.equals(deviceName);
    }

    /**
     * 检查是否应该记录此设备（简单去重）
     */
    private static boolean shouldLogDevice(String name, String address) {
        if (name == null || address == null) {
            return false;
        }

        // 只记录目标设备（根据设备名称）
        if (!isTargetDevice(name, address)) {
            return false;
        }

        long currentTime = System.currentTimeMillis();
        String deviceKey = address; // 使用地址作为设备标识

        // 如果是相同设备且在时间间隔内，则不记录
        if (deviceKey.equals(lastLoggedDevice) &&
            (currentTime - lastLogTime) < LOG_INTERVAL) {
            return false;
        }

        // 更新最后记录的设备信息
        lastLoggedDevice = deviceKey;
        lastLogTime = currentTime;
        return true;
    }

    /**
     * 检查是否应该记录此设备并分析RSSI趋势
     */
    private static boolean shouldLogDeviceWithRSSI(String name, String address, int rssi) {
        if (name == null || address == null) {
            return false;
        }

        // 只记录目标设备（根据设备名称）
        if (!isTargetDevice(name, address)) {
            return false;
        }

        long currentTime = System.currentTimeMillis();
        String deviceKey = address; // 使用地址作为设备标识

        // 如果是相同设备且在时间间隔内，则不记录
        if (deviceKey.equals(lastLoggedDevice) &&
            (currentTime - lastLogTime) < LOG_INTERVAL) {
            return false;
        }

        // 只有在确定要记录日志时，才分析距离变化
        analyzeDistanceChange(rssi);

        // 更新去重信息
        lastLoggedDevice = deviceKey;
        lastLogTime = currentTime;
        return true;
    }

    /**
     * 简单的信号区间触发算法 v5.1 - 添加调试信息
     */
    private static void analyzeDistanceChange(int rssi) {
        long currentTime = System.currentTimeMillis();
        lastRSSI = rssi;
        lastSignalTime = currentTime;

        // 判断当前是否在信号范围内
        boolean currentlyInRange = rssi >= SIGNAL_THRESHOLD;

        // 调试信息
        LogUtils.log(TAG, String.format("🔍 调试: RSSI=%d, 阈值=%d, 当前在范围内=%s, 之前在范围内=%s, 已执行解锁=%s, 已执行上锁=%s",
            rssi, SIGNAL_THRESHOLD, currentlyInRange, isInRange, hasExecutedUnlock, hasExecutedLock));

        // 状态变化检测
        if (currentlyInRange && !isInRange) {
            // 刚进入信号范围
            LogUtils.log(TAG, "🎯 检测到进入信号范围");
            isInRange = true;
            hasExecutedLock = false; // 重置上锁标记

            if (!hasExecutedUnlock) {
                LogUtils.log(TAG, String.format("📶 进入信号范围 (%d dBm >= %d dBm)，执行解锁", rssi, SIGNAL_THRESHOLD));
                executeUnlockCommand();
                hasExecutedUnlock = true;
            } else {
                LogUtils.log(TAG, "⚠️ 已在范围内执行过解锁，跳过");
            }

        } else if (!currentlyInRange && isInRange) {
            // 刚离开信号范围
            LogUtils.log(TAG, "🎯 检测到离开信号范围");
            isInRange = false;
            hasExecutedUnlock = false; // 重置解锁标记

            if (!hasExecutedLock) {
                LogUtils.log(TAG, String.format("📵 离开信号范围 (%d dBm < %d dBm)，执行上锁", rssi, SIGNAL_THRESHOLD));
                executeStatusQuery(); // 执行上锁操作
                hasExecutedLock = true;
            } else {
                LogUtils.log(TAG, "⚠️ 已在范围外执行过上锁，跳过");
            }
        } else {
            LogUtils.log(TAG, "🔄 状态无变化，不执行动作");
        }
    }

    // 删除旧的距离分析结果类

    // 删除信号消失检测方法，改为立即触发

    // 删除复杂的智能分析类，使用简单的区间触发机制

    // 删除复杂的多维度分析方法，使用简单的区间触发

    // 删除旧的高级距离分析算法

    // 删除复杂的智能分析方法，使用简单的区间触发

    // 删除复杂的行为模式识别方法

    // 删除所有复杂的智能分析方法

    // 删除所有旧的分析辅助方法，使用简单的区间触发

    // 删除复杂的智能动作处理方法

    // 删除旧的复杂距离变化动作处理方法

    /**
     * 执行解锁命令（走近时）
     */
    private static void executeUnlockCommand() {
        new Thread(() -> {
            // 获取网络唤醒锁
            boolean wakeLockAcquired = false;
            try {
                if (networkWakeLock != null && !networkWakeLock.isHeld()) {
                    networkWakeLock.acquire(30000); // 30秒超时
                    wakeLockAcquired = true;
                    LogUtils.log(TAG, "🔓 网络唤醒锁已获取，开始执行解锁命令");
                } else {
                    LogUtils.log(TAG, "🔓 网络唤醒锁不可用或已持有，继续执行解锁命令");
                }
            } catch (Exception e) {
                LogUtils.logError(TAG, "获取网络唤醒锁失败", e);
            }

            try {
                LogUtils.log(TAG, "🔓 开始发送解锁HTTP请求...");
                java.net.URL url = new java.net.URL("https://iov.edaoduo.com/aoduo/prod/api/v2/car/ctrl");
                java.net.HttpURLConnection conn = (java.net.HttpURLConnection) url.openConnection();

                // 设置超时
                conn.setConnectTimeout(10000); // 10秒连接超时
                conn.setReadTimeout(15000); // 15秒读取超时

                // 设置请求方法和头部
                conn.setRequestMethod("POST");
                conn.setRequestProperty("Token", "1bbeeea6424e2683d911ac43f7407de60dd152e596");
                conn.setRequestProperty("Signature", "cf659d41590f67320c8198c1045ee4b6");
                conn.setRequestProperty("Apiver", "v2");
                conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
                conn.setRequestProperty("User-Agent", "Dalvik/2.1.0 (Linux; U; Android 15; 24031PN0DC Build/AQ3A.240627.003)");
                conn.setRequestProperty("Host", "iov.edaoduo.com");
                conn.setRequestProperty("Connection", "Keep-Alive");
                conn.setRequestProperty("Accept-Encoding", "gzip");

                // 设置POST数据
                conn.setDoOutput(true);
                String postData = "boot_time=15&ctrl=unlock&carid=75055";
                conn.setRequestProperty("Content-Length", String.valueOf(postData.length()));

                LogUtils.log(TAG, "🔓 发送POST数据: " + postData);

                // 发送POST数据
                java.io.OutputStream os = conn.getOutputStream();
                os.write(postData.getBytes("UTF-8"));
                os.flush();
                os.close();

                // 获取响应
                int responseCode = conn.getResponseCode();
                LogUtils.log(TAG, "🔓 解锁命令执行完成，响应码: " + responseCode);

                // 读取响应内容
                if (responseCode == 200) {
                    java.io.InputStream is = conn.getInputStream();
                    java.util.Scanner scanner = new java.util.Scanner(is, "UTF-8");
                    String response = scanner.useDelimiter("\\A").hasNext() ? scanner.next() : "";
                    scanner.close();
                    is.close();
                    LogUtils.log(TAG, "🔓 解锁响应内容: " + response);
                } else {
                    LogUtils.log(TAG, "🔓 解锁请求失败，状态码: " + responseCode);
                }

                conn.disconnect();

            } catch (java.net.SocketTimeoutException e) {
                LogUtils.logError(TAG, "🔓 解锁请求超时", e);
            } catch (java.net.UnknownHostException e) {
                LogUtils.logError(TAG, "🔓 解锁请求DNS解析失败", e);
            } catch (java.io.IOException e) {
                LogUtils.logError(TAG, "🔓 解锁请求IO异常", e);
            } catch (Exception e) {
                LogUtils.logError(TAG, "🔓 解锁请求未知异常", e);
            } finally {
                // 释放网络唤醒锁
                if (wakeLockAcquired && networkWakeLock != null && networkWakeLock.isHeld()) {
                    try {
                        networkWakeLock.release();
                        LogUtils.log(TAG, "网络唤醒锁已释放");
                    } catch (Exception e) {
                        LogUtils.logError(TAG, "释放网络唤醒锁失败", e);
                    }
                }
            }
        }).start();
    }

    /**
     * 执行锁车命令（远离时）- 对应curl POST请求 ctrl=rise_window
     */
    private static void executeStatusQuery() {
        new Thread(() -> {
            // 获取网络唤醒锁
            boolean wakeLockAcquired = false;
            try {
                if (networkWakeLock != null && !networkWakeLock.isHeld()) {
                    networkWakeLock.acquire(30000); // 30秒超时
                    wakeLockAcquired = true;
                    LogUtils.log(TAG, "🔒 网络唤醒锁已获取，开始执行锁车命令");
                } else {
                    LogUtils.log(TAG, "🔒 网络唤醒锁不可用或已持有，继续执行锁车命令");
                }
            } catch (Exception e) {
                LogUtils.logError(TAG, "获取网络唤醒锁失败", e);
            }

            try {
                LogUtils.log(TAG, "🔒 开始发送锁车HTTP请求...");
                // 锁车：POST请求到 /car/ctrl，使用 ctrl=rise_window
                java.net.URL url = new java.net.URL("https://iov.edaoduo.com/aoduo/prod/api/v2/car/ctrl");
                java.net.HttpURLConnection conn = (java.net.HttpURLConnection) url.openConnection();

                // 设置超时
                conn.setConnectTimeout(10000); // 10秒连接超时
                conn.setReadTimeout(15000); // 15秒读取超时

                // 设置请求方法和头部 - 完全按照您提供的curl命令
                conn.setRequestMethod("POST");
                conn.setRequestProperty("Token", "1bbeeea6424e2683d911ac43f7407de60dd152e596");
                conn.setRequestProperty("Signature", "cf659d41590f67320c8198c1045ee4b6");
                conn.setRequestProperty("Apiver", "v2");
                conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
                conn.setRequestProperty("User-Agent", "Dalvik/2.1.0 (Linux; U; Android 15; 24031PN0DC Build/AQ3A.240627.003)");
                conn.setRequestProperty("Host", "iov.edaoduo.com");
                conn.setRequestProperty("Connection", "Keep-Alive");
                conn.setRequestProperty("Accept-Encoding", "gzip");

                // 设置POST数据
                conn.setDoOutput(true);
                String postData = "boot_time=15&ctrl=rise_window&carid=75055";
                conn.setRequestProperty("Content-Length", String.valueOf(postData.length()));

                LogUtils.log(TAG, "🔒 发送POST数据: " + postData);

                // 发送POST数据
                java.io.OutputStream os = conn.getOutputStream();
                os.write(postData.getBytes("UTF-8"));
                os.flush();
                os.close();

                // 获取响应
                int responseCode = conn.getResponseCode();
                LogUtils.log(TAG, "🔒 锁车命令执行完成，响应码: " + responseCode);

                // 读取响应内容
                if (responseCode == 200) {
                    java.io.InputStream is = conn.getInputStream();
                    java.util.Scanner scanner = new java.util.Scanner(is, "UTF-8");
                    String response = scanner.useDelimiter("\\A").hasNext() ? scanner.next() : "";
                    scanner.close();
                    is.close();
                    LogUtils.log(TAG, "🔒 锁车响应内容: " + (response.length() > 200 ? response.substring(0, 200) + "..." : response));
                } else {
                    LogUtils.log(TAG, "🔒 锁车请求失败，状态码: " + responseCode);
                }

                conn.disconnect();

            } catch (java.net.SocketTimeoutException e) {
                LogUtils.logError(TAG, "🔒 锁车请求超时", e);
            } catch (java.net.UnknownHostException e) {
                LogUtils.logError(TAG, "🔒 锁车请求DNS解析失败", e);
            } catch (java.io.IOException e) {
                LogUtils.logError(TAG, "🔒 锁车请求IO异常", e);
            } catch (Exception e) {
                LogUtils.logError(TAG, "🔒 锁车请求未知异常", e);
            } finally {
                // 释放网络唤醒锁
                if (wakeLockAcquired && networkWakeLock != null && networkWakeLock.isHeld()) {
                    try {
                        networkWakeLock.release();
                        LogUtils.log(TAG, "网络唤醒锁已释放");
                    } catch (Exception e) {
                        LogUtils.logError(TAG, "释放网络唤醒锁失败", e);
                    }
                }
            }
        }).start();
    }

    /**
     * 初始化所有蓝牙Hook
     * @param classLoader 系统类加载器
     */
    public static void initHooks(ClassLoader classLoader) {
        try {
            LogUtils.log(TAG, "=== 蓝牙监控模块启动 ===");
            LogUtils.log(TAG, "目标设备名称: " + TARGET_DEVICE_NAME);
            LogUtils.log(TAG, "开始初始化蓝牙监控Hook");

            // Hook系统Context获取
            hookSystemContext(classLoader);

            // Hook蓝牙广播接收
            hookBluetoothBroadcast(classLoader);

            // 启动主动扫描
            startActiveScanning();

            LogUtils.log(TAG, "所有蓝牙Hook初始化完成");
        } catch (Exception e) {
            LogUtils.logError(TAG, "Hook初始化失败", e);
        }
    }
    

    
    /**
     * Hook BLE扫描
     */
    private static void hookBLEScanning(ClassLoader classLoader) {
        try {
            // Hook BluetoothLeScanner.startScan()
            Class<?> bleScannerClass = XposedHelpers.findClass(
                "android.bluetooth.le.BluetoothLeScanner", classLoader);
            
            XposedHelpers.findAndHookMethod(
                bleScannerClass,
                "startScan",
                ScanCallback.class,
                new XC_MethodHook() {
                    @Override
                    protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                        LogUtils.log(TAG, "BLE扫描开始");
                        
                        // Hook ScanCallback
                        ScanCallback callback = (ScanCallback) param.args[0];
                        if (callback != null) {
                            hookScanCallback(callback);
                        }
                    }
                }
            );
            
            LogUtils.log(TAG, "BLE扫描Hook设置成功");
        } catch (Exception e) {
            LogUtils.logError(TAG, "BLE扫描Hook失败", e);
        }
    }
    
    /**
     * Hook ScanCallback以监控BLE扫描结果
     */
    private static void hookScanCallback(ScanCallback callback) {
        try {
            XposedHelpers.findAndHookMethod(
                callback.getClass(),
                "onScanResult",
                int.class,
                ScanResult.class,
                new XC_MethodHook() {
                    @Override
                    protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                        ScanResult result = (ScanResult) param.args[1];
                        if (result != null && result.getDevice() != null) {
                            BluetoothDevice device = result.getDevice();
                            int rssi = result.getRssi();
                            String name = device.getName();
                            String address = device.getAddress();

                            // 使用去重检查并分析RSSI趋势
                            if (shouldLogDeviceWithRSSI(name, address, rssi)) {
                                LogUtils.logBluetoothDevice(name, rssi, address);
                            }
                        }
                    }
                }
            );
            
            XposedHelpers.findAndHookMethod(
                callback.getClass(),
                "onBatchScanResults",
                List.class,
                new XC_MethodHook() {
                    @Override
                    protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                        @SuppressWarnings("unchecked")
                        List<ScanResult> results = (List<ScanResult>) param.args[0];
                        if (results != null) {
                            for (ScanResult result : results) {
                                if (result != null && result.getDevice() != null) {
                                    BluetoothDevice device = result.getDevice();
                                    int rssi = result.getRssi();
                                    String name = device.getName();
                                    String address = device.getAddress();

                                    // 使用去重检查并分析RSSI趋势
                                    if (shouldLogDeviceWithRSSI(name, address, rssi)) {
                                        LogUtils.logBluetoothDevice(name, rssi, address);
                                    }
                                }
                            }
                        }
                    }
                }
            );
            
        } catch (Exception e) {
            LogUtils.logError(TAG, "ScanCallback Hook失败", e);
        }
    }
    
    /**
     * Hook系统Context获取
     */
    private static void hookSystemContext(ClassLoader classLoader) {
        try {
            // Hook ActivityThread获取系统Context
            Class<?> activityThreadClass = XposedHelpers.findClass("android.app.ActivityThread", classLoader);
            XposedHelpers.findAndHookMethod(
                activityThreadClass,
                "getSystemContext",
                new XC_MethodHook() {
                    @Override
                    protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                        if (systemContext == null && param.getResult() != null) {
                            systemContext = (Context) param.getResult();
                            LogUtils.log(TAG, "获取到系统Context");
                        }
                    }
                }
            );

            LogUtils.log(TAG, "系统Context Hook设置成功");
        } catch (Exception e) {
            LogUtils.logError(TAG, "系统Context Hook失败", e);
        }
    }

    /**
     * 获取最优扫描间隔（小米14 Ultra + Android 15特殊优化）
     */
    private static long getOptimalScanInterval() {
        try {
            if (systemContext != null) {
                android.os.PowerManager powerManager = (android.os.PowerManager) systemContext.getSystemService(Context.POWER_SERVICE);
                if (powerManager != null) {
                    // 检查屏幕是否开启
                    boolean isScreenOn = powerManager.isInteractive();

                    // 检查是否在电池优化白名单中
                    boolean isIgnoringBatteryOptimizations = false;
                    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                        try {
                            String packageName = systemContext.getPackageName();
                            isIgnoringBatteryOptimizations = powerManager.isIgnoringBatteryOptimizations(packageName);
                        } catch (Exception e) {
                            LogUtils.log(TAG, "检查电池优化状态失败: " + e.getMessage());
                        }
                    }

                    // 小米设备特殊优化策略
                    boolean isXiaomi = isXiaomiDevice();
                    boolean isAndroid15 = isAndroid15();

                    if (isXiaomi && isAndroid15) {
                        // 小米14 Ultra + Android 15 特殊优化
                        LogUtils.log(TAG, "🔧 应用小米14 Ultra + Android 15特殊扫描策略");

                        if (isIgnoringBatteryOptimizations) {
                            // 白名单内：小米设备可以更激进
                            return isScreenOn ? 600 : 300;  // 屏幕开启0.6s，关闭0.3s
                        } else {
                            // 受限制：小米的电池优化更严格，需要更保守
                            return isScreenOn ? 2000 : 1500;  // 屏幕开启2s，关闭1.5s
                        }
                    } else if (isXiaomi) {
                        // 其他小米设备优化
                        if (isIgnoringBatteryOptimizations) {
                            return isScreenOn ? 700 : 350;  // 屏幕开启0.7s，关闭0.35s
                        } else {
                            return isScreenOn ? 1800 : 1200;  // 屏幕开启1.8s，关闭1.2s
                        }
                    } else {
                        // 非小米设备标准策略
                        if (isIgnoringBatteryOptimizations) {
                            return isScreenOn ? 800 : 400;  // 屏幕开启0.8s，关闭0.4s
                        } else {
                            return isScreenOn ? 1500 : 1000;  // 屏幕开启1.5s，关闭1s
                        }
                    }
                }
            }
        } catch (Exception e) {
            LogUtils.logError(TAG, "获取屏幕状态失败", e);
        }
        return 1200; // 默认1.2秒间隔
    }

    /**
     * 强制重启BLE扫描
     */
    private static void forceRestartBLEScan() {
        try {
            if (bluetoothAdapter != null && bluetoothAdapter.isEnabled()) {
                BluetoothLeScanner scanner = bluetoothAdapter.getBluetoothLeScanner();
                if (scanner != null && bleScanCallback != null) {
                    // 强制停止当前扫描
                    try {
                        scanner.stopScan(bleScanCallback);
                        LogUtils.log(TAG, "🔄 已强制停止BLE扫描");
                    } catch (Exception e) {
                        LogUtils.log(TAG, "停止BLE扫描时出现异常: " + e.getMessage());
                    }

                    // 清空回调并重新开始扫描
                    bleScanCallback = null;

                    // 短暂延迟后重新开始扫描
                    if (mainHandler != null) {
                        mainHandler.postDelayed(() -> {
                            LogUtils.log(TAG, "🔄 重新启动BLE扫描");
                            startBLEScan();
                        }, 100);
                    }
                }
            }
        } catch (Exception e) {
            LogUtils.logError(TAG, "强制重启BLE扫描失败", e);
        }
    }

    /**
     * 确保唤醒锁持续有效
     */
    private static void ensureWakeLockActive() {
        try {
            if (wakeLock != null && !wakeLock.isHeld()) {
                wakeLock.acquire();
                LogUtils.log(TAG, "重新获取蓝牙扫描唤醒锁");
            }
        } catch (Exception e) {
            LogUtils.logError(TAG, "重新获取唤醒锁失败", e);
        }
    }

    /**
     * 初始化屏幕状态监听器
     */
    private static void initScreenStateReceiver() {
        try {
            if (systemContext == null) {
                LogUtils.log(TAG, "系统Context为空，无法初始化屏幕状态监听器");
                return;
            }

            screenStateReceiver = new BroadcastReceiver() {
                @Override
                public void onReceive(Context context, Intent intent) {
                    String action = intent.getAction();
                    if (Intent.ACTION_SCREEN_OFF.equals(action)) {
                        LogUtils.log(TAG, "📱 屏幕关闭，切换到高频扫描模式");
                        // 屏幕关闭时确保唤醒锁有效
                        ensureWakeLockActive();
                    } else if (Intent.ACTION_SCREEN_ON.equals(action)) {
                        LogUtils.log(TAG, "📱 屏幕开启，切换到标准扫描模式");
                    }
                }
            };

            IntentFilter filter = new IntentFilter();
            filter.addAction(Intent.ACTION_SCREEN_ON);
            filter.addAction(Intent.ACTION_SCREEN_OFF);
            systemContext.registerReceiver(screenStateReceiver, filter);
            LogUtils.log(TAG, "屏幕状态监听器已注册");
        } catch (Exception e) {
            LogUtils.logError(TAG, "初始化屏幕状态监听器失败", e);
        }
    }

    /**
     * 检测设备厂商和系统版本
     */
    private static boolean isXiaomiDevice() {
        return "Xiaomi".equalsIgnoreCase(android.os.Build.MANUFACTURER) ||
               "Redmi".equalsIgnoreCase(android.os.Build.MANUFACTURER);
    }

    private static boolean isAndroid15() {
        return android.os.Build.VERSION.SDK_INT >= 35; // Android 15 = API 35
    }

    private static boolean isHyperOS() {
        try {
            String version = android.os.Build.VERSION.INCREMENTAL;
            return version != null && (version.contains("HyperOS") || version.contains("HYPEROS"));
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 小米设备特殊优化：请求电池优化白名单
     */
    private static void requestBatteryOptimizationWhitelist() {
        try {
            if (systemContext == null) {
                LogUtils.log(TAG, "系统Context为空，无法请求电池优化白名单");
                return;
            }

            // 检测设备信息
            boolean isXiaomi = isXiaomiDevice();
            boolean isAndroid15 = isAndroid15();
            boolean isHyperOS = isHyperOS();

            LogUtils.log(TAG, String.format("🔍 设备检测: 小米=%s, Android15=%s, HyperOS=%s, 型号=%s",
                isXiaomi, isAndroid15, isHyperOS, android.os.Build.MODEL));

            android.os.PowerManager powerManager = (android.os.PowerManager) systemContext.getSystemService(Context.POWER_SERVICE);
            if (powerManager != null && android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                String packageName = systemContext.getPackageName();
                boolean isIgnoringBatteryOptimizations = powerManager.isIgnoringBatteryOptimizations(packageName);

                LogUtils.log(TAG, "当前电池优化状态: " + (isIgnoringBatteryOptimizations ? "已忽略" : "受限制"));

                if (!isIgnoringBatteryOptimizations) {
                    if (isXiaomi) {
                        LogUtils.log(TAG, "⚠️ 小米设备检测到电池优化限制");
                        LogUtils.log(TAG, "📱 小米设备需要额外设置：");
                        LogUtils.log(TAG, "   1. 设置→应用设置→应用管理→BluetoothMonitor→省电策略→无限制");
                        LogUtils.log(TAG, "   2. 设置→应用设置→应用管理→BluetoothMonitor→自启动→开启");
                        LogUtils.log(TAG, "   3. 设置→应用设置→应用管理→BluetoothMonitor→后台弹出界面→开启");

                        if (isHyperOS) {
                            LogUtils.log(TAG, "   4. HyperOS额外设置→智能场景→应用冻结→添加BluetoothMonitor到白名单");
                        }
                    } else {
                        LogUtils.log(TAG, "⚠️ 应用受到电池优化限制，这可能影响蓝牙扫描性能");
                        LogUtils.log(TAG, "建议手动将应用添加到电池优化白名单");
                    }

                    // 尝试自动请求白名单（需要用户确认）
                    try {
                        Intent intent = new Intent();
                        intent.setAction(android.provider.Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS);
                        intent.setData(android.net.Uri.parse("package:" + packageName));
                        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                        systemContext.startActivity(intent);
                        LogUtils.log(TAG, "已发起电池优化白名单请求");
                    } catch (Exception e) {
                        LogUtils.log(TAG, "自动请求电池优化白名单失败: " + e.getMessage());
                    }
                } else {
                    LogUtils.log(TAG, "✅ 应用已在电池优化白名单中");
                }
            }
        } catch (Exception e) {
            LogUtils.logError(TAG, "检查电池优化状态失败", e);
        }
    }

    /**
     * 初始化唤醒锁，防止锁屏时休眠
     */
    private static void initWakeLocks() {
        try {
            if (systemContext == null) {
                LogUtils.log(TAG, "系统Context为空，无法初始化唤醒锁");
                return;
            }

            android.os.PowerManager powerManager = (android.os.PowerManager) systemContext.getSystemService(Context.POWER_SERVICE);
            if (powerManager != null) {
                // 创建持久唤醒锁，保持CPU运行但允许屏幕关闭
                wakeLock = powerManager.newWakeLock(
                    android.os.PowerManager.PARTIAL_WAKE_LOCK,
                    "BluetoothMonitor:ScanWakeLock"
                );
                // 移除超时限制，持续保持唤醒
                wakeLock.acquire();
                LogUtils.log(TAG, "蓝牙扫描唤醒锁已获取（持久模式）");

                // 创建网络唤醒锁，用于HTTP请求
                networkWakeLock = powerManager.newWakeLock(
                    android.os.PowerManager.PARTIAL_WAKE_LOCK,
                    "BluetoothMonitor:NetworkWakeLock"
                );
                LogUtils.log(TAG, "网络请求唤醒锁已创建");
            }

            // 请求电池优化白名单
            requestBatteryOptimizationWhitelist();

            // 小米设备特殊优化指南
            if (XiaomiOptimizationHelper.isXiaomiDevice()) {
                XiaomiOptimizationHelper.printOptimizationGuide();
                // 可选：自动打开设置页面（需要用户交互）
                // XiaomiOptimizationHelper.openXiaomiSettings(systemContext);
            }

        } catch (Exception e) {
            LogUtils.logError(TAG, "初始化唤醒锁失败", e);
        }
    }

    /**
     * 启动主动扫描
     */
    private static void startActiveScanning() {
        try {
            // 延迟启动，等待系统初始化完成
            new Thread(() -> {
                try {
                    LogUtils.log(TAG, "开始启动主动扫描，等待5秒...");
                    Thread.sleep(5000); // 等待5秒

                    if (systemContext == null) {
                        LogUtils.log(TAG, "系统Context未获取到，尝试其他方式");
                        return;
                    }

                    LogUtils.log(TAG, "系统Context已获取，开始初始化蓝牙");

                    // 初始化唤醒锁，防止锁屏时休眠
                    initWakeLocks();

                    // 初始化屏幕状态监听器
                    initScreenStateReceiver();

                    // 启动前台服务确保持续运行
                    BluetoothForegroundService.startService(systemContext);

                    // 尝试多种方式获取蓝牙适配器
                    bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
                    if (bluetoothAdapter == null) {
                        LogUtils.log(TAG, "getDefaultAdapter()返回null，尝试BluetoothManager");
                        BluetoothManager bluetoothManager = (BluetoothManager) systemContext.getSystemService(Context.BLUETOOTH_SERVICE);
                        if (bluetoothManager != null) {
                            bluetoothAdapter = bluetoothManager.getAdapter();
                            LogUtils.log(TAG, "通过BluetoothManager获取适配器: " + (bluetoothAdapter != null));
                        }
                    } else {
                        LogUtils.log(TAG, "通过getDefaultAdapter()获取适配器成功");
                    }

                    if (bluetoothAdapter == null) {
                        LogUtils.log(TAG, "无法获取蓝牙适配器");
                        return;
                    }

                    // 详细检查蓝牙状态
                    int state = bluetoothAdapter.getState();
                    LogUtils.log(TAG, "蓝牙状态: " + state + " (10=ON, 12=OFF)");
                    LogUtils.log(TAG, "isEnabled(): " + bluetoothAdapter.isEnabled());

                    if (!bluetoothAdapter.isEnabled()) {
                        LogUtils.log(TAG, "蓝牙未开启，状态码: " + state);
                        // 即使蓝牙未开启，也继续尝试其他操作
                    }

                    // 创建主线程Handler
                    mainHandler = new Handler(Looper.getMainLooper());
                    LogUtils.log(TAG, "主线程Handler创建成功");

                    // 注册蓝牙广播接收器
                    registerBluetoothReceiver();

                    // 获取已配对设备
                    logPairedDevices();

                    // 启动定期扫描
                    startPeriodicScanning();

                    // 静默启动，不输出日志

                } catch (Exception e) {
                    // 静默处理错误
                }
            }).start();

        } catch (Exception e) {
            LogUtils.logError(TAG, "启动主动扫描线程失败", e);
        }
    }

    /**
     * 注册蓝牙广播接收器
     */
    private static void registerBluetoothReceiver() {
        try {
            bluetoothReceiver = new BroadcastReceiver() {
                @Override
                public void onReceive(Context context, Intent intent) {
                    String action = intent.getAction();
                    if (BluetoothDevice.ACTION_FOUND.equals(action)) {
                        BluetoothDevice device = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);
                        short rssi = intent.getShortExtra(BluetoothDevice.EXTRA_RSSI, Short.MIN_VALUE);

                        if (device != null) {
                            String name = device.getName();
                            String address = device.getAddress();

                            // 使用去重检查
                            if (shouldLogDevice(name, address)) {
                                LogUtils.logBluetoothDevice(name, (int)rssi, address);
                            }
                        }
                    }
                }
            };

            IntentFilter filter = new IntentFilter();
            filter.addAction(BluetoothDevice.ACTION_FOUND);

            systemContext.registerReceiver(bluetoothReceiver, filter);
            LogUtils.log(TAG, "蓝牙广播接收器注册成功");

        } catch (Exception e) {
            LogUtils.logError(TAG, "注册蓝牙广播接收器失败", e);
        }
    }

    /**
     * 启动定期扫描
     */
    private static void startPeriodicScanning() {
        try {
            LogUtils.log(TAG, "启动定期BLE扫描（3秒间隔）");

            // 防重复启动
            if (isPeriodicScanningStarted) {
                LogUtils.log(TAG, "定期扫描已在运行，跳过");
                return;
            }
            isPeriodicScanningStarted = true;

            // 🔧 修复关键bug：初始化lastSignalTime为当前时间
            lastSignalTime = System.currentTimeMillis();
            LogUtils.log(TAG, "✅ 初始化lastSignalTime: " + lastSignalTime);

            Runnable scanTask = new Runnable() {
                @Override
                public void run() {
                    try {
                        // 检查信号消失（如果之前在范围内，但现在超过8秒没收到信号）
                        long currentTime = System.currentTimeMillis();
                        long timeSinceLastSignal = currentTime - lastSignalTime;

                        // 每30秒输出一次状态信息用于调试
                        if (currentTime % 30000 < 800) { // 在30秒周期内的前0.8秒输出
                            // 🔧 修复：只显示合理的时间差
                            String timeDisplay = timeSinceLastSignal < 86400000 ?
                                String.format("%dms", timeSinceLastSignal) : "初始化中";
                            LogUtils.log(TAG, String.format("🔍 状态检查: 在范围内=%s, 距离上次信号=%s, 最后RSSI=%d, 唤醒锁状态=%s",
                                isInRange, timeDisplay, lastRSSI,
                                (wakeLock != null && wakeLock.isHeld()) ? "持有" : "未持有"));
                        }

                        // 🔧 修复：只有在合理的时间范围内才检查信号超时
                        // 如果超过30秒没有信号，且之前有过信号，才强制重启BLE扫描
                        if (timeSinceLastSignal > 30000 && timeSinceLastSignal < 86400000) { // 30秒到24小时之间
                            LogUtils.log(TAG, String.format("⚠️ 长时间无信号（%dms），强制重启BLE扫描", timeSinceLastSignal));
                            forceRestartBLEScan();
                            // 重置时间避免连续重启
                            lastSignalTime = System.currentTimeMillis();
                        }

                        // 🔧 修复：只有在合理时间范围内且确实在范围内时才检查信号消失
                        if (isInRange && timeSinceLastSignal > 15000 && timeSinceLastSignal < 86400000) {
                            LogUtils.log(TAG, String.format("🎯 检测到信号消失超时（%dms > 15000ms）", timeSinceLastSignal));
                            // 模拟信号消失，触发离开范围逻辑
                            analyzeDistanceChange(-999); // 使用很低的RSSI值表示信号消失
                        }

                        // 确保唤醒锁持续有效
                        ensureWakeLockActive();

                        // 静默扫描，不输出状态日志
                        if (bluetoothAdapter != null && bluetoothAdapter.isEnabled()) {
                            // 只启动BLE扫描
                            startBLEScan();
                        }

                        // 根据屏幕状态调整扫描间隔
                        long scanInterval = getOptimalScanInterval();
                        if (mainHandler != null) {
                            mainHandler.postDelayed(this, scanInterval);
                        }

                    } catch (Exception e) {
                        LogUtils.logError(TAG, "扫描任务异常", e);
                        isPeriodicScanningStarted = false;
                    }
                }
            };

            // 立即执行第一次
            if (mainHandler != null) {
                mainHandler.post(scanTask);
            }

        } catch (Exception e) {
            // 静默处理错误
            isPeriodicScanningStarted = false; // 重置状态，允许重试
        }
    }







    /**
     * 启动BLE扫描（增强版本，确保锁屏时正常工作）
     */
    private static void startBLEScan() {
        try {
            if (bluetoothAdapter == null || !bluetoothAdapter.isEnabled()) {
                LogUtils.log(TAG, "BLE扫描跳过：蓝牙适配器不可用或未启用");
                return;
            }

            BluetoothLeScanner scanner = bluetoothAdapter.getBluetoothLeScanner();
            if (scanner == null) {
                LogUtils.log(TAG, "BLE扫描跳过：无法获取BLE扫描器");
                return;
            }

            LogUtils.log(TAG, "开始BLE扫描（增强模式）");

            // 强制停止之前的扫描
            if (bleScanCallback != null) {
                try {
                    scanner.stopScan(bleScanCallback);
                    LogUtils.log(TAG, "已停止之前的BLE扫描");
                } catch (Exception e) {
                    LogUtils.log(TAG, "停止之前扫描时出现异常: " + e.getMessage());
                }
                bleScanCallback = null;
            }

            bleScanCallback = new ScanCallback() {
                @Override
                public void onScanResult(int callbackType, ScanResult result) {
                    try {
                        if (result != null && result.getDevice() != null) {
                            BluetoothDevice device = result.getDevice();
                            int rssi = result.getRssi();
                            String name = device.getName();
                            String address = device.getAddress();

                            // 使用去重检查并分析RSSI趋势
                            if (shouldLogDeviceWithRSSI(name, address, rssi)) {
                                LogUtils.logBluetoothDevice(name, rssi, address);
                            }
                        }
                    } catch (Exception e) {
                        // 静默处理单个结果的错误
                    }
                }

                @Override
                public void onBatchScanResults(List<ScanResult> results) {
                    // 简化：不处理批量结果，避免复杂性
                }

                @Override
                public void onScanFailed(int errorCode) {
                    LogUtils.logError(TAG, "BLE扫描失败，错误代码: " + errorCode, null);
                    bleScanCallback = null;
                }
            };

            // 使用最高性能扫描设置，针对小米14 Ultra + Android 15特殊优化
            try {
                ScanSettings.Builder settingsBuilder = new ScanSettings.Builder();

                // 检测设备类型并应用特殊设置
                boolean isXiaomi = isXiaomiDevice();
                boolean isAndroid15 = isAndroid15();

                if (isXiaomi && isAndroid15) {
                    // 小米14 Ultra + Android 15 特殊设置
                    LogUtils.log(TAG, "🔧 应用小米14 Ultra + Android 15特殊BLE扫描设置");
                    settingsBuilder
                        .setScanMode(ScanSettings.SCAN_MODE_LOW_POWER)  // 小米设备用低功耗模式更稳定
                        .setReportDelay(100)  // 小延迟报告，避免系统限制
                        .setCallbackType(ScanSettings.CALLBACK_TYPE_ALL_MATCHES);
                } else if (isXiaomi) {
                    // 其他小米设备设置
                    LogUtils.log(TAG, "🔧 应用小米设备特殊BLE扫描设置");
                    settingsBuilder
                        .setScanMode(ScanSettings.SCAN_MODE_BALANCED)  // 平衡模式
                        .setReportDelay(50)
                        .setCallbackType(ScanSettings.CALLBACK_TYPE_ALL_MATCHES);
                } else {
                    // 非小米设备标准高性能设置
                    settingsBuilder
                        .setScanMode(ScanSettings.SCAN_MODE_LOW_LATENCY)  // 低延迟模式
                        .setReportDelay(0)  // 立即报告结果
                        .setCallbackType(ScanSettings.CALLBACK_TYPE_ALL_MATCHES);  // 所有匹配回调
                }

                // 添加Android 6.0+的高级设置
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                    if (isXiaomi) {
                        // 小米设备使用保守的匹配设置
                        settingsBuilder.setMatchMode(ScanSettings.MATCH_MODE_STICKY)  // 粘性匹配模式
                                      .setNumOfMatches(ScanSettings.MATCH_NUM_ONE_ADVERTISEMENT);  // 单个匹配
                    } else {
                        // 非小米设备使用激进设置
                        settingsBuilder.setMatchMode(ScanSettings.MATCH_MODE_AGGRESSIVE)  // 积极匹配模式
                                      .setNumOfMatches(ScanSettings.MATCH_NUM_MAX_ADVERTISEMENT);  // 最大匹配数
                    }
                }

                ScanSettings settings = settingsBuilder.build();
                scanner.startScan(null, settings, bleScanCallback);
            } catch (Exception e) {
                LogUtils.logError(TAG, "启动BLE扫描失败", e);
                bleScanCallback = null;
            }

        } catch (Exception e) {
            LogUtils.logError(TAG, "BLE扫描方法异常", e);
            bleScanCallback = null;
        }
    }

    /**
     * 直接扫描附近设备
     */
    private static void scanNearbyDevicesDirectly() {
        try {
            LogUtils.log(TAG, "开始直接扫描附近设备...");

            // 尝试通过反射强制扫描
            if (bluetoothAdapter != null) {
                try {
                    // 强制启动发现，忽略状态检查
                    java.lang.reflect.Method startDiscoveryMethod = BluetoothAdapter.class.getDeclaredMethod("startDiscovery");
                    startDiscoveryMethod.setAccessible(true);
                    Object result = startDiscoveryMethod.invoke(bluetoothAdapter);
                    LogUtils.log(TAG, "反射调用startDiscovery结果: " + result);
                } catch (Exception e) {
                    LogUtils.log(TAG, "反射调用startDiscovery失败: " + e.getMessage());
                }

                // 尝试获取当前连接的设备
                try {
                    java.lang.reflect.Method getConnectedDevicesMethod = BluetoothAdapter.class.getDeclaredMethod("getConnectedDevices");
                    getConnectedDevicesMethod.setAccessible(true);
                    Object connectedDevices = getConnectedDevicesMethod.invoke(bluetoothAdapter);
                    LogUtils.log(TAG, "当前连接设备: " + connectedDevices);
                } catch (Exception e) {
                    LogUtils.log(TAG, "获取连接设备失败: " + e.getMessage());
                }
            }

        } catch (Exception e) {
            LogUtils.logError(TAG, "直接扫描设备失败", e);
        }
    }

    /**
     * 获取已配对设备
     */
    private static void logPairedDevices() {
        try {
            if (bluetoothAdapter != null) {
                LogUtils.log(TAG, "开始获取已配对设备...");

                try {
                    Set<BluetoothDevice> pairedDevices = bluetoothAdapter.getBondedDevices();
                    LogUtils.log(TAG, "已配对设备数量: " + pairedDevices.size());

                    int targetDeviceCount = 0;
                    for (BluetoothDevice device : pairedDevices) {
                        String name = device.getName();
                        String address = device.getAddress();

                        // 只记录目标设备
                        if (isTargetDevice(name, address)) {
                            LogUtils.logBluetoothDevice(name, -50, address);
                            targetDeviceCount++;
                        }
                    }

                    if (targetDeviceCount > 0) {
                        LogUtils.log(TAG, "已配对的目标设备数量: " + targetDeviceCount);
                    } else {
                        LogUtils.log(TAG, "未找到已配对的目标设备");
                    }
                } catch (SecurityException e) {
                    LogUtils.log(TAG, "获取已配对设备权限不足: " + e.getMessage());
                }
            } else {
                LogUtils.log(TAG, "蓝牙适配器为null，无法获取已配对设备");
            }
        } catch (Exception e) {
            LogUtils.logError(TAG, "获取已配对设备失败", e);
        }
    }

    /**
     * Hook蓝牙广播接收
     */
    private static void hookBluetoothBroadcast(ClassLoader classLoader) {
        try {
            // Hook Intent处理，监控蓝牙设备发现广播
            Class<?> intentClass = Intent.class;

            // 这里可以Hook BroadcastReceiver的相关方法
            // 由于系统复杂性，我们主要依赖上面的直接Hook方式

            LogUtils.log(TAG, "蓝牙广播Hook设置成功");
        } catch (Exception e) {
            LogUtils.logError(TAG, "蓝牙广播Hook失败", e);
        }
    }
}
