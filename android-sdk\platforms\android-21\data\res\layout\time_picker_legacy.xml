<?xml version="1.0" encoding="utf-8"?>
<!--
**
** Copyright 2007, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<!-- Layout of time picker-->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/timePickerLayout"
    android:orientation="horizontal"
    android:layout_gravity="center_horizontal"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <LinearLayout android:orientation="horizontal"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layoutDirection="ltr">

        <!-- hour -->
        <NumberPicker
            android:id="@+id/hour"
            android:layout_width="70dip"
            android:layout_height="wrap_content"
            android:focusable="true"
            android:focusableInTouchMode="true"
            />

        <!-- minute -->
        <NumberPicker
            android:id="@+id/minute"
            android:layout_width="70dip"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dip"
            android:focusable="true"
            android:focusableInTouchMode="true"
            />

    </LinearLayout>

    <!-- AM / PM -->
    <Button
        android:id="@+id/amPm"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="43dip"
        android:layout_marginStart="5dip"
        android:paddingStart="20dip"
        android:paddingEnd="20dip"
        style="?android:attr/textAppearanceLargeInverse"
        android:textColor="@android:color/primary_text_light_nodisable"
        />

</LinearLayout>
