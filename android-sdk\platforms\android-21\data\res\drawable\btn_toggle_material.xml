<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2008 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<inset xmlns:android="http://schemas.android.com/apk/res/android"
       android:insetLeft="@dimen/button_inset_horizontal_material"
       android:insetTop="@dimen/button_inset_vertical_material"
       android:insetRight="@dimen/button_inset_horizontal_material"
       android:insetBottom="@dimen/button_inset_vertical_material">
    <layer-list android:paddingMode="stack">
        <item>
            <ripple android:color="?attr/colorControlHighlight">
                <item>
                    <shape xmlns:android="http://schemas.android.com/apk/res/android"
                           android:shape="rectangle">
                        <corners android:topLeftRadius="@dimen/control_corner_material"
                                 android:topRightRadius="@dimen/control_corner_material"/>
                        <solid android:color="?attr/colorButtonNormal" />
                        <padding android:left="@dimen/button_padding_horizontal_material"
                                 android:top="@dimen/button_padding_vertical_material"
                                 android:right="@dimen/button_padding_horizontal_material"
                                 android:bottom="@dimen/button_padding_vertical_material" />
                    </shape>
                </item>
            </ripple>
        </item>
        <item>
            <selector xmlns:android="http://schemas.android.com/apk/res/android">
                <item android:state_checked="false">
                    <nine-patch android:src="@drawable/btn_toggle_indicator_mtrl_alpha"
                        android:tint="?attr/colorControlNormal" />
                </item>
                <item android:state_checked="true">
                    <nine-patch android:src="@drawable/btn_toggle_indicator_mtrl_alpha"
                        android:tint="?attr/colorControlActivated" />
                </item>
            </selector>
        </item>
    </layer-list>
</inset>
