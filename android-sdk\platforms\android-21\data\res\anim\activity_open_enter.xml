<?xml version="1.0" encoding="utf-8"?>
<!--
/*
** Copyright 2009, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<set xmlns:android="http://schemas.android.com/apk/res/android"
        android:shareInterpolator="false"
        android:zAdjustment="top">
    <alpha android:fromAlpha="0.0" android:toAlpha="1.0"
            android:interpolator="@interpolator/decelerate_quart"
            android:fillEnabled="true"
            android:fillBefore="false" android:fillAfter="true"
            android:duration="200"/>
    <translate android:fromYDelta="8%" android:toYDelta="0"
            android:fillEnabled="true" android:fillBefore="true" android:fillAfter="true"
            android:interpolator="@interpolator/decelerate_quint"
            android:duration="350"/>
</set>