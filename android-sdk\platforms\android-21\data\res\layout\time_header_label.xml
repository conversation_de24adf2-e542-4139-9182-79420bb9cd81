<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2013 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License
  -->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/time_header"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center">
        <TextView
            android:id="@+id/hours"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toLeftOf="@+id/separator"
            android:layout_alignBaseline="@+id/separator" />
        <TextView
            android:id="@+id/separator"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingLeft="@dimen/timepicker_separator_padding"
            android:paddingRight="@dimen/timepicker_separator_padding"
            android:layout_centerInParent="true"
            android:importantForAccessibility="no" />
        <TextView
            android:id="@+id/minutes"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toRightOf="@+id/separator"
            android:layout_alignBaseline="@+id/separator" />
        <TextView
            android:id="@+id/ampm_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingLeft="@dimen/timepicker_ampm_left_padding"
            android:paddingRight="@dimen/timepicker_ampm_left_padding"
            android:layout_toRightOf="@+id/separator"
            android:layout_alignBaseline="@+id/separator" />
    </RelativeLayout>
</FrameLayout>
