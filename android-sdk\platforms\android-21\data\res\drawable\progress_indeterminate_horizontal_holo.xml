<?xml version="1.0" encoding="utf-8"?>
<!--
/*
** Copyright 2011, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License"); 
** you may not use this file except in compliance with the License. 
** You may obtain a copy of the License at 
**
**     http://www.apache.org/licenses/LICENSE-2.0 
**
** Unless required by applicable law or agreed to in writing, software 
** distributed under the License is distributed on an "AS IS" BASIS, 
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. 
** See the License for the specific language governing permissions and 
** limitations under the License.
*/
-->
<animation-list
        xmlns:android="http://schemas.android.com/apk/res/android"
        android:oneshot="false">
    <item android:drawable="@drawable/progressbar_indeterminate_holo1" android:duration="50" />
    <item android:drawable="@drawable/progressbar_indeterminate_holo2" android:duration="50" />
    <item android:drawable="@drawable/progressbar_indeterminate_holo3" android:duration="50" />
    <item android:drawable="@drawable/progressbar_indeterminate_holo4" android:duration="50" />
    <item android:drawable="@drawable/progressbar_indeterminate_holo5" android:duration="50" />
    <item android:drawable="@drawable/progressbar_indeterminate_holo6" android:duration="50" />
    <item android:drawable="@drawable/progressbar_indeterminate_holo7" android:duration="50" />
    <item android:drawable="@drawable/progressbar_indeterminate_holo8" android:duration="50" />
</animation-list>
