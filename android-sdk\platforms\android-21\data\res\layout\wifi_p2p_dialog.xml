<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2011 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout android:id="@+id/info"
            style="@style/wifi_section" />

        <LinearLayout android:id="@+id/enter_pin_section"
            style="@style/wifi_section"
            android:visibility="gone">

            <LinearLayout
                style="@style/wifi_item">
                <TextView
                    android:text="@string/wifi_p2p_enter_pin_message"
                    style="@style/wifi_item_label" />

                <EditText android:id="@+id/wifi_p2p_wps_pin"
                        android:singleLine="true"
                        android:maxLines="8"
                        android:inputType="number"
                        style="@style/wifi_item_content" />
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>

</ScrollView>
