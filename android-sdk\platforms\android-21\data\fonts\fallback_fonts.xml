<?xml version="1.0" encoding="utf-8"?>
<!--
    NOTE: this file is the legacy format, for compatibility with apps. The new,
    more flexible format is fonts.xml. Please keep the two in sync until the legacy
    format can be fully removed.

    Fallback Fonts

    This file specifies the fonts, and the priority order, that will be searched for any
    glyphs not handled by the default fonts specified in /system/etc/system_fonts.xml.
    Each entry consists of a family tag and a list of files (file names) which support that
    family. The fonts for each family are listed in the order of the styles that they
    handle (the order is: regular, bold, italic, and bold-italic). The order in which the
    families are listed in this file represents the order in which these fallback fonts
    will be searched for glyphs that are not supported by the default system fonts (which are
    found in /system/etc/system_fonts.xml).

    Note that there is not nameset for fallback fonts, unlike the fonts specified in
    system_fonts.xml. The ability to support specific names in fallback fonts may be supported
    in the future. For now, the lack of files entries here is an indicator to the system that
    these are fallback fonts, instead of default named system fonts.

    There is another optional file in /vendor/etc/fallback_fonts.xml. That file can be used to
    provide references to other font families that should be used in addition to the default
    fallback fonts. That file can also specify the order in which the fallback fonts should be
    searched, to ensure that a vendor-provided font will be used before another fallback font
    which happens to handle the same glyph.

    Han languages (Chinese, Japanese, and Korean) share a common range of unicode characters;
    their ordering in the fallback or vendor files gives priority to the first in the list.
    Language-specific ordering can be configured by adding a BCP 47-style "lang" attribute to
    a "file" element; fonts matching the language of text being drawn will be prioritised over
    all others.
-->
<familyset>
    <family>
        <fileset>
            <file variant="elegant">NotoNaskh-Regular.ttf</file>
            <file variant="elegant">NotoNaskh-Bold.ttf</file>
        </fileset>
    </family>
    <family>
        <fileset>
            <file variant="compact">NotoNaskhUI-Regular.ttf</file>
            <file variant="compact">NotoNaskhUI-Bold.ttf</file>
        </fileset>
    </family>
    <family>
        <fileset>
            <file>NotoSansEthiopic-Regular.ttf</file>
            <file>NotoSansEthiopic-Bold.ttf</file>
        </fileset>
    </family>
    <family>
        <fileset>
            <file>NotoSansHebrew-Regular.ttf</file>
            <file>NotoSansHebrew-Bold.ttf</file>
        </fileset>
    </family>
    <family>
        <fileset>
            <file variant="elegant">NotoSansThai-Regular.ttf</file>
            <file variant="elegant">NotoSansThai-Bold.ttf</file>
        </fileset>
    </family>
    <family>
        <fileset>
            <file variant="compact">NotoSansThaiUI-Regular.ttf</file>
            <file variant="compact">NotoSansThaiUI-Bold.ttf</file>
        </fileset>
    </family>
    <family>
        <fileset>
            <file>NotoSansArmenian-Regular.ttf</file>
            <file>NotoSansArmenian-Bold.ttf</file>
        </fileset>
    </family>
    <family>
        <fileset>
            <file>NotoSansGeorgian-Regular.ttf</file>
            <file>NotoSansGeorgian-Bold.ttf</file>
        </fileset>
    </family>
    <family>
        <fileset>
            <file variant="elegant">NotoSansDevanagari-Regular.ttf</file>
            <file variant="elegant">NotoSansDevanagari-Bold.ttf</file>
        </fileset>
    </family>
    <family>
        <fileset>
            <file variant="compact">NotoSansDevanagariUI-Regular.ttf</file>
            <file variant="compact">NotoSansDevanagariUI-Bold.ttf</file>
        </fileset>
    </family>
    <!-- Gujarati should come after Devanagari -->
    <family>
        <fileset>
            <file variant="elegant">NotoSansGujarati-Regular.ttf</file>
            <file variant="elegant">NotoSansGujarati-Bold.ttf</file>
        </fileset>
    </family>
    <family>
        <fileset>
            <file variant="compact">NotoSansGujaratiUI-Regular.ttf</file>
            <file variant="compact">NotoSansGujaratiUI-Bold.ttf</file>
        </fileset>
    </family>
    <!-- Gurmukhi should come after Devanagari -->
    <family>
        <fileset>
            <file variant="elegant">NotoSansGurmukhi-Regular.ttf</file>
            <file variant="elegant">NotoSansGurmukhi-Bold.ttf</file>
        </fileset>
    </family>
    <family>
        <fileset>
            <file variant="compact">NotoSansGurmukhiUI-Regular.ttf</file>
            <file variant="compact">NotoSansGurmukhiUI-Bold.ttf</file>
        </fileset>
    </family>
    <family>
        <fileset>
            <file variant="elegant">NotoSansTamil-Regular.ttf</file>
            <file variant="elegant">NotoSansTamil-Bold.ttf</file>
        </fileset>
    </family>
    <family>
        <fileset>
            <file variant="compact">NotoSansTamilUI-Regular.ttf</file>
            <file variant="compact">NotoSansTamilUI-Bold.ttf</file>
        </fileset>
    </family>
    <family>
        <fileset>
            <file variant="elegant">NotoSansMalayalam-Regular.ttf</file>
            <file variant="elegant">NotoSansMalayalam-Bold.ttf</file>
        </fileset>
    </family>
    <family>
        <fileset>
            <file variant="compact">NotoSansMalayalamUI-Regular.ttf</file>
            <file variant="compact">NotoSansMalayalamUI-Bold.ttf</file>
        </fileset>
    </family>
    <family>
        <fileset>
            <file variant="elegant">NotoSansBengali-Regular.ttf</file>
            <file variant="elegant">NotoSansBengali-Bold.ttf</file>
        </fileset>
    </family>
    <family>
        <fileset>
            <file variant="compact">NotoSansBengaliUI-Regular.ttf</file>
            <file variant="compact">NotoSansBengaliUI-Bold.ttf</file>
        </fileset>
    </family>
    <family>
        <fileset>
            <file variant="elegant">NotoSansTelugu-Regular.ttf</file>
            <file variant="elegant">NotoSansTelugu-Bold.ttf</file>
        </fileset>
    </family>
    <family>
        <fileset>
            <file variant="compact">NotoSansTeluguUI-Regular.ttf</file>
            <file variant="compact">NotoSansTeluguUI-Bold.ttf</file>
        </fileset>
    </family>
    <family>
        <fileset>
            <file variant="elegant">NotoSansKannada-Regular.ttf</file>
            <file variant="elegant">NotoSansKannada-Bold.ttf</file>
        </fileset>
    </family>
    <family>
        <fileset>
            <file variant="compact">NotoSansKannadaUI-Regular.ttf</file>
            <file variant="compact">NotoSansKannadaUI-Bold.ttf</file>
        </fileset>
    </family>
    <family>
        <fileset>
            <file>NotoSansSinhala-Regular.ttf</file>
            <file>NotoSansSinhala-Bold.ttf</file>
        </fileset>
    </family>
    <family>
        <fileset>
            <file variant="elegant">NotoSansKhmer-Regular.ttf</file>
            <file variant="elegant">NotoSansKhmer-Bold.ttf</file>
        </fileset>
    </family>
    <family>
        <fileset>
            <file variant="compact">NotoSansKhmerUI-Regular.ttf</file>
            <file variant="compact">NotoSansKhmerUI-Bold.ttf</file>
        </fileset>
    </family>
    <family>
        <fileset>
            <file variant="elegant">NotoSansLao-Regular.ttf</file>
            <file variant="elegant">NotoSansLao-Bold.ttf</file>
        </fileset>
    </family>
    <family>
        <fileset>
            <file variant="compact">NotoSansLaoUI-Regular.ttf</file>
            <file variant="compact">NotoSansLaoUI-Bold.ttf</file>
        </fileset>
    </family>
    <family>
        <fileset>
            <file variant="elegant">NotoSansMyanmar-Regular.ttf</file>
            <file variant="elegant">NotoSansMyanmar-Bold.ttf</file>
        </fileset>
    </family>
    <family>
        <fileset>
            <file variant="compact">NotoSansMyanmarUI-Regular.ttf</file>
            <file variant="compact">NotoSansMyanmarUI-Bold.ttf</file>
        </fileset>
    </family>
    <family>
        <fileset>
            <file>NotoSansCherokee-Regular.ttf</file>
        </fileset>
    </family>
    <family>
        <fileset>
            <file>NotoSansCanadianAboriginal-Regular.ttf</file>
        </fileset>
    </family>
    <family>
        <fileset>
            <file>NotoSansYi-Regular.ttf</file>
        </fileset>
    </family>
    <family>
        <fileset>
            <file lang="zh-Hans">NotoSansHans-Regular.otf</file>
        </fileset>
    </family>
    <family>
        <fileset>
            <file lang="zh-Hant">NotoSansHant-Regular.otf</file>
        </fileset>
    </family>
    <family>
        <fileset>
            <file lang="ja">NotoSansJP-Regular.otf</file>
        </fileset>
    </family>
    <family>
        <fileset>
            <file lang="ko">NotoSansKR-Regular.otf</file>
        </fileset>
    </family>
    <family>
        <fileset>
            <file>NanumGothic.ttf</file>
        </fileset>
    </family>
    <family>
        <fileset>
            <file>NotoSansSymbols-Regular-Subsetted.ttf</file>
        </fileset>
    </family>
    <family>
        <fileset>
            <file>NotoColorEmoji.ttf</file>
        </fileset>
    </family>
    <family>
        <fileset>
            <file>DroidSansFallback.ttf</file>
        </fileset>
    </family>
    <family>
        <fileset>
            <file lang="ja">MTLmr3m.ttf</file>
        </fileset>
    </family>
    <!-- Note: complex scripts (i.e. those requiring shaping in Harfbuzz) have
         a cumulative limit of 64k glyphs. Thus, if they are placed after the
         large fonts such as DroidSansFallback, they are likely to render
         incorrectly. Please use caution when putting fonts toward the end of
         the list.
    -->
</familyset>
